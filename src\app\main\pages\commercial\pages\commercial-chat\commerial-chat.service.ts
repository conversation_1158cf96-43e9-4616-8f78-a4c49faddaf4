import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { Observable, BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class CommercialChatService {
  private isShowFileSubject = new BehaviorSubject<boolean>(false);
  isShowFile$ = this.isShowFileSubject.asObservable();
  private showLoadingFileSubject = new BehaviorSubject<boolean>(false);
  showLoadingFile$ = this.showLoadingFileSubject.asObservable();

  constructor(
    private _http: HttpClient
  ) { }

  showFileRef() { this.isShowFileSubject.next(true); }
  hideFileRef() { this.isShowFileSubject.next(false); }
  toggleFileRef() { this.isShowFileSubject.next(!this.isShowFileSubject.value); }

  setshowLoadingFile(isLoading: boolean) {
    this.showLoadingFileSubject.next(isLoading);
  }

  getDetailFile(fileId: string, doc_type?: string): Observable<any> {
    const params: Params = {
      document_id: fileId,
    };
    if (doc_type) params['doc_type'] = doc_type;

    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get<any>(`${environment.apiUrl}/legal_search/by_query`, {
      params,
      headers,
    });
  }
  
}