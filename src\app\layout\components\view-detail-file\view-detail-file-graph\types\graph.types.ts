/**
 * Type definitions for graph component
 */

export interface ApiNode {
  nhan_ui?: string;
  nhan?: string;
  id_ui?: string;
  thuoc_tinh?: NodeAttributes;
  ten_day_du?: string;
  metadata?: {
    color?: string;
  };
}

export interface NodeAttributes {
  ID?: string | number;
  doc_ID?: number;
  ten_day_du?: string;
  so_hieu?: string;
  ngay_ban_hanh?: string;
  loai_van_ban?: string;
  ngay_co_hieu_luc?: string;
  ngay_dang_cong_bao?: string;
  co_quan_ban_hanh?: string;
  chuc_danh?: string;
  nguoi_ky?: string;
  pham_vi?: string;
  trich_yeu?: string;
  tieu_de?: string;
  tinh_trang_hieu_luc?: string;
  thoi_gian_cap_nhat?: string;
  vi_tri?: string;
}

export interface ApiRelationship {
  source_id: string | number;
  target_id: string | number;
  loai_moi_quan_he?: string;
  huong?: 'OUTGOING' | 'INCOMING';
  strength?: number;
}

export interface GraphFilterOptions {
  relationship_types?: string[];
  co_quan_ban_hanh?: string[];
  tinh_trang_hieu_luc?: string[];
  loai_van_ban?: string[];
}

export interface GraphApiResponse {
  nodes?: ApiNode[];
  relationships?: ApiRelationship[];
  seed_node_ids?: (string | number)[];
  total_nodes?: number;
  total_relationships?: number;
  // Dynamic filter options extracted from current graph
  filter_options?: GraphFilterOptions;
}

export type DateFilterMode = "ban_hanh" | "hieu_luc" | null;

export interface GraphFormState {
  search_legal_term: string;
  isSearchAdvance: boolean;
  selectedBoLocMoiQuanHe: string[];
  selectedTinhTrangHieuLuc: string[];
  selectedBoLocLoaiVanBan: string[];
  selectedCoQuanBanHanh: string[];
  depth: number;
  global_limit: number;
  limit_per_seed: number;
  dateFilterMode: DateFilterMode;
  dateFilterFrom: number | null;
  dateFilterTo: number | null;
  depthError?: string;
  global_limitError?: string;
  limit_per_seedError?: string;
}

export interface GraphRequestBody {
  co_quan_ban_hanh: string[];
  depth?: number;
  global_limit?: number;
  limit_per_seed?: number;
  loai_van_ban: string[];
  node_ids: string[];
  relationship_types: string[];
  target_node_type: string;
  tinh_trang_hieu_luc: string[];
  ban_hanh_year_from: number | null;
  ban_hanh_year_to: number | null;
  hieu_luc_year_from: number | null;
  hieu_luc_year_to: number | null;
}

export interface DocumentData {
  ten_day_du: string;
  so_hieu: string;
  ngay_ban_hanh: string;
  loai_van_ban: string;
  ngay_co_hieu_luc: string;
  ngay_dang_cong_bao: string;
  co_quan_ban_hanh: string;
  chuc_danh: string;
  nguoi_ky: string;
  pham_vi: string;
  trich_yeu: string;
  tinh_trang_hieu_luc: string;
  thoi_gian_cap_nhat: string;
}

export interface ExpansionHistory {
  nodeIds: Set<string>;
  relationshipKeys: Set<string>;
}

export interface ContextMenuItem {
  apiNode: ApiNode;
  nodeId: string;
  hiddenDescendants: Set<string>;
  canRestore: boolean;
  isRoot: boolean;
}

export interface GraphNode {
  id: string;
  label: string;
  color?: string;
}

export interface GraphLink {
  id: string;
  source: string;
  target: string;
  label?: string;
  strength?: number;
  __curveness?: number;
  __isBaiBo?: boolean;
  __relationshipType?: string;
}

export interface DocumentListItem {
  id: string;
  title: string;
  selected: boolean;
  apiNode: ApiNode;
}

export interface DocumentTableRow extends DocumentListItem {
  loai_van_ban: string;
  so_hieu: string;
  co_quan_ban_hanh: string;
  ngay_ban_hanh: string;
  ngay_co_hieu_luc: string;
  tinh_trang_hieu_luc: string;
}

