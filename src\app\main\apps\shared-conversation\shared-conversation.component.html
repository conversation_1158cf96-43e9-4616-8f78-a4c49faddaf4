<div class="shared-conversation sc-page bg-white">
  <app-shared-conversation-header
    [scrolled]="isContentScrolled"
  ></app-shared-conversation-header>

  <!-- <div
    class="shared-conversation-content d-flex justify-content-center"
    #scrollContainer
    (scroll)="onContentScroll(scrollContainer)"
  >
    <div>
      <app-shared-conversation-content></app-shared-conversation-content>
    </div>

    <app-shared-conversation-footer></app-shared-conversation-footer>
  </div> -->
  <div
    class="shared-conversation-content sc-content d-flex flex-column align-items-center"
    #scrollContainer
    (scroll)="onContentScroll(scrollContainer)"
  >
    <div class="content-wrapper">
      <app-shared-conversation-content
        [shareId]="shareId"
      ></app-shared-conversation-content>
    </div>
  </div>
</div>
