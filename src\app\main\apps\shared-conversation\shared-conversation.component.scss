// .shared-conversation {
//   display: flex;
//   flex-direction: column;
//   height: 100vh;
//   overflow: hidden;

//   .shared-conversation-content {
//     flex: 1;
//     overflow-y: auto;
//     background: #fff;
//   }
// }
.shared-conversation {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;

  .shared-conversation-content {
    flex: 1;
    overflow-y: auto;
  }

  .content-wrapper {
    width: 100%;
    max-width: 1000px;
  }

  app-shared-conversation-footer {
    margin-top: auto;
  }
}
