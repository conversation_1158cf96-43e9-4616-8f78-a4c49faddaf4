import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { QuanLyVanBanService } from '../../quan-ly-van-ban/quan-ly-van-ban.service';
import { FormType } from 'app/models/FormType';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-workspace-selection-modal',
  templateUrl: './workspace-selection-modal.component.html',
  styleUrls: ['./workspace-selection-modal.component.scss']
})
export class WorkspaceSelectionModalComponent implements OnInit {
  @Input() selectedFiles: any[] = [];

  public listWorkSpace: any[] = [];
  public selectedWorkspaces: any[] = [];
  public isLoading: boolean = false;
  public isCreatingWorkspace: boolean = false;
  public searchWorkspace: string = '';
  public currentPage: number = 1;
  public pageSize: number = 10;
  public totalWorkspaces: number = 0;

  constructor(
    private quanLyVanBanService: QuanLyVanBanService,
    private toast: ToastrService,
    private modalService: NgbModal,
    private activeModal: NgbActiveModal
  ) { }
  @ViewChild("modalWorkSpace") modalWorkSpace!: any;
  public type: FormType;
  public title: string;
  public row: any;
  ngOnInit(): void {
    this.loadWorkspaces();
  }

  loadWorkspaces(): void {
    this.isLoading = true;
    this.quanLyVanBanService
      .getAllWorkSpace(this.searchWorkspace, this.currentPage, this.pageSize)
      .pipe(finalize(() => { this.isLoading = false; }))
      .subscribe({
        next: (res) => {
          this.listWorkSpace = res.results || [];
          this.totalWorkspaces = res.count || 0;
        },
        error: (error) => {
          this.toast.error('Không thể tải danh sách không gian dự án', 'Lỗi', {
            closeButton: true,
            positionClass: 'toast-top-right',
            toastClass: 'toast ngx-toastr',
          });
        }
      });
  }

  onWorkspaceToggle(workspace: any, event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (isChecked) {
      if (!this.selectedWorkspaces.find(w => w.id === workspace.id)) {
        this.selectedWorkspaces.push(workspace);
      }
    } else {
      this.selectedWorkspaces = this.selectedWorkspaces.filter(w => w.id !== workspace.id);
    }
  }

  isWorkspaceSelected(workspace: any): boolean {
    return this.selectedWorkspaces.some(w => w.id === workspace.id);
  }

  onSearchChange(): void {
    this.currentPage = 1;
    this.loadWorkspaces();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadWorkspaces();
  }

  createNewWorkspace(): void {
    const workspaceName = prompt('Nhập tên không gian dự án mới:');
    if (!workspaceName || workspaceName.trim() === '') {
      return;
    }

    const workspaceData = {
      name: workspaceName.trim(),
      description: ''
    };

    this.quanLyVanBanService.addWorkSpace(workspaceData).subscribe({
      next: (response) => {
        this.toast.success('Tạo không gian dự án thành công', 'Thành công', {
          closeButton: true,
          positionClass: 'toast-top-right',
          toastClass: 'toast ngx-toastr',
        });

        // Thêm workspace mới vào danh sách
        const newWorkspace = {
          id: response.id,
          name: response.name,
          description: response.description || ''
        };
        this.listWorkSpace.unshift(newWorkspace);

        // Tự động chọn workspace vừa tạo
        this.selectedWorkspaces = [newWorkspace];
      },
      error: (error) => {
        this.toast.error('Tạo không gian dự án thất bại', 'Lỗi', {
          closeButton: true,
          positionClass: 'toast-top-right',
          toastClass: 'toast ngx-toastr',
        });
      }
    });
  }

  saveToDefaultWorkspace(): void {
    // Lưu vào workspace mặc định (từ localStorage)
    this.activeModal.close({
      useDefaultWorkspace: true,
      selectedFiles: this.selectedFiles
    });
  }

  saveToSelectedWorkspaces(): void {
    if (this.selectedWorkspaces.length === 0) {
      this.toast.warning('Vui lòng chọn ít nhất một không gian dự án', 'Cảnh báo', {
        closeButton: true,
        positionClass: 'toast-top-right',
        toastClass: 'toast ngx-toastr',
      });
      return;
    }

    // Trả về danh sách workspace được chọn
    this.activeModal.close({
      selectedWorkspaces: this.selectedWorkspaces,
      selectedFiles: this.selectedFiles
    });
  }

  cancel(): void {
    this.activeModal.dismiss();
  }

  selectAll(): void {
    this.selectedWorkspaces = [...this.listWorkSpace];
  }

  clearAll(): void {
    this.selectedWorkspaces = [];
  }

  // Kiểm tra xem tất cả workspace có được chọn không
  isAllSelected(): boolean {
    return this.listWorkSpace.length > 0 && this.selectedWorkspaces.length === this.listWorkSpace.length;
  }

  // Kiểm tra trạng thái indeterminate (một số được chọn)
  isIndeterminate(): boolean {
    return this.selectedWorkspaces.length > 0 && this.selectedWorkspaces.length < this.listWorkSpace.length;
  }

  // Toggle chọn tất cả/bỏ chọn tất cả
  toggleSelectAll(event: any): void {
    if (event.target.checked) {
      this.selectAll();
    } else {
      this.clearAll();
    }
  }

  onChildDestroyed(): void {
    // Reload workspace list khi tạo mới workspace thành công
    this.loadWorkspaces();
  }
  addWorkSpace() {
    this.modalOpen(
      this.modalWorkSpace,
      "Thêm dự án",
      null,
      FormType.Create,
      "sm",
      false
    );
  }
  modalOpen(modalSM, title: string, row: any, type: FormType, size: string, shouldExpandOnSmallScreen: boolean = true) {
    const isSmallScreen = window.innerWidth < 992;
    console.log(size)

    console.log(title)
    console.log(type)
    const modalRef = this.modalService.open(modalSM, {
      centered: true,
      size: size,
      // Dùng modalDialogClass (Angular >= 15 + ng-bootstrap >= 15 hỗ trợ)
      modalDialogClass: (isSmallScreen && shouldExpandOnSmallScreen) ? 'modal-full-lg' : '',
    });

    this.title = title;
    this.type = type;
    this.row = row;

    // Lắng nghe khi modal đóng thành công
    modalRef.result.then((result) => {
      // Nếu tạo workspace thành công, reload danh sách
      if (result && result.success && type === FormType.Create) {
        console.log('Workspace created successfully:', result.workspace);
        this.loadWorkspaces();
        // Không cần hiển thị toast ở đây vì WorkSpaceControlComponent đã hiển thị rồi
      }
    }).catch((error) => {
      // Modal bị dismiss (đóng không thành công) - không làm gì
      console.log('Modal dismissed:', error);
    });
  }
}
