<div class="main-chat-area d-flex h-100">
  <div class="clsc-chat flex-grow-1">
    <!-- SIDEBAR LỊCH SỬ -->
    <aside class="clsc-chat__sidebar">
      <div class="clsc-chat__sidebar-header">
        <button class="clsc-chat__new-btn" (click)="addNewConversation()">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="15"
            height="15"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="mr-50 new-chat-icon"
          >
            <path d="M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z" />
            <path d="M12 8v6" />
            <path d="M9 11h6" />
          </svg>
          <span>Chat mới</span>
        </button>
      </div>

      <div class="clsc-chat__sidebar-section">
        <div class="clsc-chat__sidebar-title">Lịch sử</div>
        
        <div *ngIf="isFetchingConversation" class="h-100">
          <app-commercial-loading
            width="150"
          ></app-commercial-loading>
        </div>

        <div *ngIf="!isFetchingConversation">
          <p class="clsc-chat__no-conversation text-muted" *ngIf="listConversation.length === 0">
            Hãy bắt đầu cuộc trò chuyện đầu tiên của bạn!
          </p>

          <button
            *ngFor="let conv of listConversation"
            class="clsc-chat__session"
            [class.clsc-chat__session--active]="conv.id === selectedConversation?.id"
            (click)="selectConversation(conv)"
            (contextmenu)="openSessionMenu($event, conv)"
          >
            <div class="clsc-chat__session-row">
              <div class="clsc-chat__session-text">
                <ng-container *ngIf="conversationEditing?.id === conv.id; else viewTitle">
                  <input
                    #titleInput
                    id="session-input-{{ conv.id }}"
                    class="clsc-chat__session-input w-100"
                    [(ngModel)]="editTitle"
                    (keydown.enter)="saveNewName(conversationEditing, editTitle)"
                    (blur)="saveNewName(conversationEditing, editTitle)"
                  />
                  <div class="clsc-chat__session-date">
                    {{ conv.created_at | date:'HH:mm dd-MM-yyyy' }}
                  </div>
                </ng-container>

                <!-- Hiển thị bình thường -->
                <ng-template #viewTitle>
                  <div class="clsc-chat__session-name">{{ conv.name }}</div>
                  <div class="clsc-chat__session-date">{{ conv.created_at | date:'HH:mm dd-MM-yyyy' }}</div>
                </ng-template>
              </div>

              <!-- Nút sửa tên -->
              <!-- <button
                class="clsc-chat__session-rename"
                type="button"
                (click)="startRename(s, $event)"
              >
                <i data-feather="edit-2"></i>
              </button> -->
            </div>
          </button>
        </div>
      </div>
    </aside>

    <!-- MAIN CHAT AREA -->
    <section class="clsc-chat__main">
      <!-- HEADER -->
      <header class="clsc-chat__header">
        <div class="clsc-chat__header-left">
          <h2 class="clsc-chat__title">{{ selectedConversation?.name || "Phiên làm việc mới" }}</h2>
          <p class="clsc-chat__subtitle d-flex align-items-center">
            <span class="subtitle-animate-pulse"></span>
            C-LegalLM Pro
          </p>
        </div>

        <!-- BỎ “Về trang Landing” -->
        <!--
        <div class="clsc-chat__header-right">
          <a routerLink="/home" class="clsc-chat__link">Về trang Landing</a>
        </div>
        -->
      </header>

      <!-- TOOLBAR -->
      <!-- <div class="clsc-chat__toolbar">
        <button
          class="clsc-chip"
          [class.clsc-chip--active]="useRepo"
          (click)="useRepo = !useRepo"
        >
          Kho dữ liệu
        </button>

        <button
          class="clsc-chip clsc-chip--green"
          [class.clsc-chip--active]="useGoogle"
          (click)="useGoogle = !useGoogle"
        >
          Google Search
        </button>

        <button
          class="clsc-chip clsc-chip--red"
          [class.clsc-chip--active]="modelMode === 'auto'"
          (click)="modelMode = modelMode === 'auto' ? 'c-legal' : 'auto'"
        >
          {{ modelMode === "auto" ? "Tự động" : "C-LegalLM Pro" }}
        </button>

        <div class="clsc-chat__toolbar-spacer"></div>

        <div class="clsc-chat__role">
          <span class="clsc-chat__role-label">Vai trò:</span>
          <select [(ngModel)]="selectedRole" class="clsc-chat__role-select">
            <option *ngFor="let r of roles" [value]="r">
              {{ r }}
            </option>
          </select>
        </div>
      </div> -->

      <!-- BODY -->
      <div class="clsc-chat__body" #chatBody>
        <div class="h-100 w-100" *ngIf="isFetchingConversation || isFetchingMessage">
          <app-commercial-loading
            width="320"
            spinnerSize="2.8"
            spinnerBW="0.28"
          ></app-commercial-loading>
        </div>

        <div *ngIf="!isFetchingConversation && !isFetchingMessage">
          <ng-container *ngIf="messages.length === 0; else hasMessages">
            <div class="clsc-chat__empty">
              <!-- <div class="clsc-chat__empty-icon">⚖️</div> -->
              <h3 class="clsc-chat__empty-title">C-LegalLM Pro</h3>
              <p class="clsc-chat__empty-desc">
                Trợ lý pháp lý thông minh, hỗ trợ tra cứu đa nguồn và thẩm định rủi
                ro pháp lý cho phòng Pháp chế / Ban Điều hành.
              </p>

              <div class="clsc-chat__suggest-grid">
                <button
                  class="clsc-suggest"
                  *ngFor="let q of quickSuggestions"
                  (click)="sendFromSuggestion(q)"
                >
                  <div class="clsc-suggest__icon">💡</div>
                  <div class="clsc-suggest__text">{{ q }}</div>
                  <!-- <div class="clsc-suggest__tag">Đa nguồn</div> -->
                </button>
              </div>
            </div>
            <div class="clsc-chat__thinking" *ngIf="thinkingStatus">
              {{ thinkingStatus }}
            </div>
          </ng-container>

          <ng-template #hasMessages>
            <div class="clsc-chat__messages">
              <div
                *ngFor="let message of messages; let last = last; let i = index"
                class="message-bubble p-0 clsc-msg"
                [class.clsc-msg--user]="message.role === 'user'"
              >
                <div class="clsc-msg__avatar">
                  <svg
                    *ngIf="message.role === 'assistant'"
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="ai-icon"
                  >
                    <path d="m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z" />
                    <path d="m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z" />
                    <path d="M7 21h10" />
                    <path d="M12 3v18" />
                    <path d="M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2" />
                  </svg>

                  <svg
                    *ngIf="message.role === 'user'"
                    xmlns="http://www.w3.org/2000/svg"
                    width="19"
                    height="19"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="user-icon"
                  >
                    <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0" />
                    <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                  </svg>
                </div>

                <div class="clsc-msg__content">
                  <div class="clsc-msg__bubble">
                    <div
                      *ngIf="message.thinking && message.thinking !== '<think>'"
                      class="clsc-msg__thinking"
                      [ngClass]="{
                        user: message.role === 'user',
                        bot: message.role === 'assistant'
                      }"
                    >
                      <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
                        <ngb-panel id="ngb-panel-1" [open]="true">
                          <ng-template ngbPanelTitle>
                            <span class="lead collapse-title card-title">
                              <strong class="font-sm">Tiến trình tư duy</strong>
                            </span>
                          </ng-template>
                          <ng-template ngbPanelContent>
                            <p
                              class="thinking mt-1 wrap-text"
                              [innerHTML]="message.thinking"
                            ></p>
                          </ng-template>
                        </ngb-panel>
                      </ngb-accordion>
                    </div>

                    <div
                      *ngIf="message.role === 'user'"
                      class="clsc-msg__markdown wrap-text"
                    >
                      <ng-container *ngIf="message.selection_text">
                        <div class="bubble-header">
                          <img
                            class="icon"
                            src="assets/images/icons/selection-text.svg"
                            alt="selection_text"
                          />
                          <p
                            class="selection-text"
                            [ngbTooltip]="message.selection_text"
                          >
                            {{ message.selection_text }}
                          </p>
                        </div>
                      </ng-container>

                      <span
                        class="d-flex"
                        [ngClass]="{
                          'ml-25':
                            message.selected_files?.upload_files?.length ||
                            message.selected_files?.save_files?.length
                        }"
                        [innerHTML]="message.answer"
                      ></span>

                      <!-- FILE ATTACH -->
                      <ng-container
                        *ngIf="
                          message.selected_files?.upload_files?.length ||
                          message.selected_files?.save_files?.length
                        "
                      >
                        <div class="file-list-wrapper">
                          <button
                            class="collapse-btn p-0"
                            (click)="collapsed = !collapsed"
                          >
                            Tài liệu đính kèm
                            <img
                              [src]="
                                collapsed
                                  ? 'assets/images/icons/up.svg'
                                  : 'assets/images/icons/down-black.svg'
                              "
                            />
                          </button>

                          <div class="file-list" [class.collapsed]="collapsed">
                            <ng-container
                              *ngFor="let file of message.selected_files.upload_files"
                            >
                              <div
                                class="file-item mb-50"
                                [ngbTooltip]="file.name"
                                placement="left"
                                container="body"
                                (click)="onOpenDocument(file)"
                                [ngClass]="{ active: isActiveFile(file) }"
                              >
                                {{ file.name }}
                              </div>
                            </ng-container>

                            <ng-container
                              *ngFor="let file of message.selected_files.save_files"
                            >
                              <div
                                class="file-item mb-50"
                                [ngbTooltip]="file.name"
                                placement="left"
                                container="body"
                                (click)="onOpenDocument(file)"
                                [ngClass]="{ active: isActiveFile(file) }"
                              >
                                {{ file.name }}
                              </div>
                            </ng-container>
                          </div>
                        </div>
                      </ng-container>
                    </div>

                    <!-- STREAMING STATUS -->
                    <div
                      *ngIf="last && !checkDoneThinking"
                      class="clsc-msg__thinking-inline"
                    >
                      {{ statusThinking }}
                    </div>

                    <!-- ASSISTANT MESSAGE -->
                    <div
                      *ngIf="message.role === 'assistant'"
                      class="clsc-msg__markdown"
                      [attr.id]="'msg-' + i"
                      (click)="onAnswerClick($event)"
                    >
                      <div
                        *ngIf="message.answerTop"
                        [innerHTML]="message.answerTop | markdown | safe"
                      ></div>

                      <div
                        *ngIf="!message.answerTop && !message.fileAttached"
                        [innerHTML]="clearAnswerTags(message.answer) | markdown | safe"
                      ></div>

                      <div
                        *ngIf="message.answerBottom"
                        class="answer-text-bottom mt-50"
                        [innerHTML]="message.answerBottom | markdown | safe"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </div>
      </div>

      <div class="clsc-chat__followups-wrapper">
        <button
          class="scroll-to-bottom-btn"
          *ngIf="showScrollBottom"
          (click)="scrollToBottom(true)"
        >
          <i data-feather="arrow-down" class="scroll-to-bottom-icon" size="22"></i>
        </button>

        <!-- FOLLOW UP -->
        <div class="clsc-chat__followups" *ngIf="followUps.length">
          <button
            class="clsc-followup"
            *ngFor="let f of followUps"
            (click)="sendFromFollowUp(f)"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="15"
              height="15"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="mr-50 new-chat-icon followups-new-chat-icon"
            >
              <path d="M22 17a2 2 0 0 1-2 2H6.828a2 2 0 0 0-1.414.586l-2.202 2.202A.71.71 0 0 1 2 21.286V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2z" />
              <path d="M12 8v6" />
              <path d="M9 11h6" />
            </svg>
            <span>{{ f }}</span>
          </button>
        </div>
      </div>

      <!-- INPUT BAR -->
      <div class="clsc-chat__input">
        <div class="clsc-chat__input-inner">
          <!-- <button class="clsc-input__addon">
            <span data-feather="paperclip"></span>
          </button> -->

          <textarea
            class="clsc-input__textarea"
            [(ngModel)]="inputValue"
            placeholder="Nhập câu hỏi pháp lý..."
            rows="1"
            (keydown)="handleKeyDownSendMsg($event)"
          ></textarea>

          <button
            class="clsc-input__send"
            [disabled]="!inputValue.trim()"
            (click)="sendMessage()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M4.698 4.034l16.302 7.966l-16.302 7.966a.503 .503 0 0 1 -.546 -.124a.555 .555 0 0 1 -.12 -.568l2.468 -7.274l-2.468 -7.274a.555 .555 0 0 1 .12 -.568a.503 .503 0 0 1 .546 -.124z" />
              <path d="M6.5 12h14.5" />
            </svg>
          </button>
        </div>

        <div class="clsc-quota text-muted w-100 d-flex justify-content-center mt-50">
          <span class="clsc-quota-text">
            Đã sử dụng {{ quota.request_count || 0 }} / {{ quota.limit_request || 0 }} lượt truy vấn. Câu trả lời có thể gặp lỗi, vui lòng kiểm tra lại thông tin.
          </span>
        </div>
      </div>

    </section>

    <div
      class="clsc-context"
      *ngIf="ctxMenu.open"
      [ngStyle]="{ left: ctxMenu.x + 'px', top: ctxMenu.y + 'px' }"
      (click)="$event.stopPropagation()"
    >
      <button 
        class="clsc-context__item d-flex align-items-center"
        (click)="prepareRenameConversation(ctxMenu.selectConversation)"
      >
        <i data-feather="edit-2" class="clsc-context__icon mr-50"></i>
        <span>Đổi tên</span>
      </button>

      <button 
        class="clsc-context__item clsc-context__item--danger d-flex align-items-center"
        (click)="prepareDeleteConversation(ctxMenu.selectConversation)"  
      >
        <i data-feather="trash-2" class="clsc-context__icon mr-50"></i>
        <span>Xóa</span>
      </button>
    </div>
    <div
      class="clsc-modal-backdrop"
      *ngIf="deleteModal.open && conversationDeleting"
      (click)="closeDeleteModal()"
    >
      <div class="clsc-modal" (click)="$event.stopPropagation()">
        <div class="clsc-modal__title">Xóa đoạn chat?</div>

        <div class="clsc-modal__desc">
          Hành động này sẽ xóa đoạn chat
          <b>"{{ conversationDeleting?.name }}"</b>.
        </div>

        <div class="clsc-modal__actions">
          <button class="clsc-btn clsc-btn--ghost" (click)="closeDeleteModal()">
            Hủy bỏ
          </button>

          <button class="clsc-btn clsc-btn--danger" (click)="confirmDeleteConversation()">
            Xóa
          </button>
        </div>
      </div>
    </div>
  </div>

  <div
    class="view-detail-file slider-panel"
    [style.width.px]="panelWidth"
    *ngIf="isShowFile$ | async"
  >
    <div
      class="resize-handle"
      (mousedown)="startResize($event)"
    >
      <!-- <span class="resize-icon">||</span> -->
      <span data-feather="more-vertical" size="22"></span>
    </div>
    
    <app-detail-file
      [fileData]="fileData"
      [termId]="termIdRef"
    ></app-detail-file>
  </div>
</div>
