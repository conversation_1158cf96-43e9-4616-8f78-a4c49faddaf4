<div class="chatbot">
  <div class="chatbot__hero">
    
    <div class="chatbot__intro anim-1">
      <div class="chatbot__sparkles">
        <img 
          src="assets/images/pages/landing/starcmcai.svg" 
          alt="icon" 
          width="28" 
          height="28"
        />
      </div>
      <div class="chatbot__welcome-text">
        <div class="text-gray">Xin chào, tôi là Trợ lý AI phục vụ</div>
        <div class="text-blue"><PERSON><PERSON><PERSON> d<PERSON>, rà soát văn bản quy phạm pháp luật hàng đầu Việt Nam.</div>
      </div>
    </div>

    <h2 class="chatbot__title">
      Tôi có thể giúp gì cho bạn?
    </h2>

    <div class="chatbot__search anim-3">
      <textarea
        #inputEl
        class="chatbot__input"
        [formControl]="form.controls['prompt']"
        (keydown)="onEnter($event)"
        (input)="onInputChange()"
      ></textarea>

      <div 
        class="chatbot__placeholder"
        *ngIf="!form.value?.prompt"
        [class.is-fading-out]="isFadingOut"
        (transitionend)="onPlaceholderTransitionEnd($event)">
        {{ currentPlaceholder }}
      </div>
      
      <button
        type="button"
        class="chatbot__send"
        title="Gửi"
        (click)="send()"
        [disabled]="loading || !form.controls['prompt']?.value?.trim()">
        <svg viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
          <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"></path>
        </svg>
      </button>
    </div>

    <div class="chatbot__chips anim-3">
      <button class="chip" *ngFor="let s of suggestions" (click)="pickSuggestion(s)">
        {{ s }}
      </button>
    </div>

  </div>
</div>