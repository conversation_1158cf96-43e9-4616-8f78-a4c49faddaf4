import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChildren, ViewChild, ElementRef, <PERSON><PERSON><PERSON>ist, HostListener, AfterViewInit, ViewEncapsulation } from "@angular/core";
import { environment } from "environments/environment";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from "@angular/platform-browser";
import { marked } from "marked";
import { Subject } from "rxjs";
import { takeUntil, tap, filter, finalize, switchMap } from "rxjs/operators";

import { ToastrService } from "ngx-toastr";

import { AuthenticationService } from "app/auth/service";
import { ChatbotService } from "app/main/apps/quan-ly-van-ban/detail-work-space/chatbot/chatbot.service";
import { CommercialService } from "../../commercial.service";
import { CommercialChatService } from "./commerial-chat.service";
import { randomUuidv4 } from "../../../../../../../util/randomUUID";

type Role = "user" | "assistant";

interface ChatSource {
  title: string;
  type: "web" | "org" | "personal";
  page?: string;
}

interface ChatMessage {
  id?: string;
  role: Role;
  content: string;
  sources?: ChatSource[];
}

interface ChatSession {
  id: number;
  title: string;
  date: string;
  messages: ChatMessage[];
  backendConversationId?: string | null;
}

const DATA_SOURCE_OPTIONS = [
  {
    value: 1,
    label: "Kho VBQPPL",
    icon: "assets/images/icons/database.svg",
  },
  {
    value: 2,
    label: "Google",
    icon: "assets/images/icons/search.svg",
  },
];


@Component({
  selector: "app-commercial-chat",
  templateUrl: "./commercial-chat.component.html",
  styleUrls: ["./commercial-chat.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class CommercialChatComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChildren("titleInput") titleInputs!: QueryList<ElementRef<HTMLInputElement>>;
  @ViewChild("chatBody") chatBody?: ElementRef<HTMLDivElement>;

  private scrollRaf: number | null = null;
  private readonly SCROLL_BOTTOM_THRESHOLD = 140;
  // danh sách phiên làm việc
  sessions: ChatSession[] = [this.createDefaultSession()];

  editingSessionId: number | null = null;
  editTitle = "";
  activeSessionId = 1;
  currentUser: any;
  userName = "";
  userInitials = "MN";
  private _destroy$ = new Subject<void>();
  // toggle tool bar
  useRepo = true;
  useGoogle = false;
  modelMode: "auto" | "c-legal" = "auto";

  // chọn vai trò
  roles = ["Trợ lý Pháp chế chung", "Chuyên gia Hợp đồng"];
  selectedRole = this.roles[0];
  private readonly STORAGE_KEY = "commercial_chat_sessions";
  private readonly GREETING_STRINGS = [
    "Xin chào, tôi là trợ lý pháp chế AI. Bạn cần hỗ trợ vấn đề gì?",
    "Xin chào, bạn là trợ lý pháp chế AI. Tôi cần hỗ trợ vấn đề gì?",
  ];

  private createDefaultSession(): ChatSession {
    return {
      id: 1,
      title: "Phiên làm việc mới",
      date: "Hôm nay",
      messages: [],
      backendConversationId: null,
    };
  }

  inputValue = "";
  conversationId: string | null = null;
  isStreaming = false;
  thinkingStatus = "";
  // follow-up demo
  followUps: string[] = [
    "Chuyển nhượng cổ phần phải nộp thuế gì?",
    "Các chế độ thai sản, ốm đau, nghỉ ốm dài ngày được hưởng gì?",
    "Hợp đồng điện tử có giá trị pháp lý không?",
  ];

  ctxMenu = {
    open: false,
    x: 0,
    y: 0,
    selectConversation: null,
  };

  deleteModal = {
    open: false,
    selectConversation: null,
  };

  // gợi ý quick card ở empty state
  quickSuggestions = [
    "Đánh giá tác động của Dự thảo Luật trí tuệ nhân tạo đối với các khối ngành liên quan",
    "Quy trình thành lập công ty TNHH/CTCP cần hồ sơ gì?",
    "Chi phí nào được đưa vào chi phí hợp lý khi tính thuế TNDN?",
    "Thủ tục hòa giải thương mại được thực hiện thế nào?",
  ];

  get activeSession(): ChatSession | undefined {
    return this.sessions.find((s) => s.id === this.activeSessionId);
  }

  private abortController: AbortController | null = null;
  workspaceId: string = null;
  checkDoneAnswer = true;
  public bodyMessage: {
    role: string;
    content: string;
    selection_text: string;
    selected_save_files: any[];
    selected_upload_files: any[];
  }[] = [];
  public textAskChatbot: string = null;
  public typeDocument: string = null;
  public selection_text: string = null;
  public selectedFile: any[] = [];
  public selected_save_files = [];
  public selected_upload_files = [];
  public toolSelected = [
    {
      value: 1,
      label: "Kho VBQPPL",
      icon: "assets/images/icons/database.svg",
    },
  ];
  public listTool = [
    {
      value: "data-source",
      label: "Nguồn dữ liệu",
      icon: "assets/images/icons/databasee.svg",
      children: DATA_SOURCE_OPTIONS,
    },
    {
      value: 3,
      label: "Tải câu trả lời",
      icon: "assets/images/icons/file-export.svg",
    },
    {
      value: 4,
      label: "Tư duy",
      icon: "assets/images/icons/bulb.svg",
      badgeIcon: "assets/images/icons/bulb-blue.svg",
    },
    {
      value: 5,
      label: "Soạn thảo",
      icon: "assets/images/icons/pencil.svg",
      badgeIcon: "assets/images/icons/pencil-blue.svg",
    },
  ];
  public isCreativeMode: boolean = false;
  public checkDoneThinking = false;
  public statusThinking: string = "";
  public unSubAll: Subject<any> = new Subject();
  public thinkingText: string = "";
  public answerText: string = "";
  public quota: any = {};
  public listConversation: any[] = [];
  public selectedConversation: any = null;
  public messages: any[] = [];
  isFetchingConversation: boolean = false;
  isFetchingMessage: boolean = false;
  conversationEditing: any = null;
  conversationDeleting: any = null;
  showScrollBottom = false;
  isShowFile$ = this._clscChatService.isShowFile$;
  fileData: any = null;
  termIdRef: string = null;
  panelWidth = 650;
  private resizing = false;

  constructor(
    private sanitizer: DomSanitizer,
    private authService: AuthenticationService,
    private _chatbotService: ChatbotService,
    private _toastService: ToastrService,
    private _clscService: CommercialService,
    private _clscChatService: CommercialChatService
  ) {

  }

  ngAfterViewInit(): void {
    // khi vừa render xong lần đầu (ví dụ load lại lịch sử)
    // this.scrollToBottom(true);
    const el = this.chatBody.nativeElement;
    el.addEventListener('scroll', () => {
      this.showScrollBottom = !this.isNearBottom(el);
    });
  }

  private isNearBottom(el: HTMLDivElement): boolean {
    const distance = el.scrollHeight - el.scrollTop - el.clientHeight;
    return distance < this.SCROLL_BOTTOM_THRESHOLD;
  }

  scrollToBottom(force = false, animation = true): void {
    const el = this.chatBody?.nativeElement;
    if (!el) return;

    // chỉ auto-scroll nếu đang gần đáy hoặc force
    if (!force && !this.isNearBottom(el)) return;

    if (animation) {
      el.scrollTo({
        top: el.scrollHeight,
        behavior: "smooth",
      });
    } else {
      el.scrollTop = el.scrollHeight;
    }
  }

  scrollToBottom2(): void {
    try {
      const container = this.chatBody.nativeElement;
      // Cuộn thẳng xuống cuối cùng
      container.scrollTop = container.scrollHeight;
    } catch (err) {
      console.error("Scroll failed:", err);
    }
  }

  private scheduleScroll(force = false): void {
    if (this.scrollRaf != null) return;
    this.scrollRaf = requestAnimationFrame(() => {
      this.scrollRaf = null;
      this.scrollToBottom(force);
    });
  }

  ngOnInit(): void {
    this.workspaceId = localStorage.getItem("workspace_id");
    this.getQuota();
    this.getConversations(this.workspaceId, true);
  }

  getConversations(workspace_id: string, showLoading = false) {
    this.isFetchingConversation = showLoading;

    this._chatbotService
      .getConversation(workspace_id)
      .pipe(finalize(() => { this.isFetchingConversation = false; }))
      .subscribe({
        next: (res: any) => {
          this.listConversation = res;
          if (res.length > 0) {
            this.selectedConversation = res[0];
            this.getMessageOnConversation(res[0], true);
          } else {
            this.messages = [];
            this.selectedConversation = null;
          }
        },
        error: (err) => {
          console.error("getConversation error", err);
        }
      });
  }

  selectConversation(conversation: any) {
    this.cancelChat();
    this.selectedConversation = conversation;
    this.getMessageOnConversation(conversation, true);
  }

  prepareRenameConversation(conversation: any) {
    this.conversationEditing = conversation;
    this.editTitle = conversation.name;
    this.closeSessionMenu();

    setTimeout(() => {
      const inputEl = this.titleInputs?.last?.nativeElement;
      if (inputEl) {
        inputEl.focus();
        const len = inputEl.value.length;
        inputEl.setSelectionRange(len, len);
      }
    });
  }

  clearEditing() {
    this.conversationEditing = null;
    this.editTitle = null;
  }

  saveNewName(item: any, newName: string) {
    if (newName === item.name) {
      this.clearEditing();
      return;
    }

    if (newName.length > 255 || newName.length == 0) {
      this._toastService.error(
        newName.length > 255
          ? "Không được vượt quá 255 ký tự!"
          : "Tên không được để trống!",
        "Lỗi",
        {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        }
      );

      this.clearEditing();
      return;
    } else {
      item.name = newName;
    }

    this._chatbotService
      .renameChatbot(item.id, newName)
      .pipe(finalize(() => { this.clearEditing(); }))
      .subscribe({
        next: (res) => {
          this._toastService.success("Đã cập nhật tên chatbot", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          item.updated_at = new Date().toISOString(); // Cập nhật thời gian sửa đổi
        },
        error: (err) => {
          this._toastService.error("Có lỗi xảy ra khi cập nhật tên chatbot!", "Lỗi", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      }
    );
    
  }

  prepareDeleteConversation(conversation: any) {
    this.conversationDeleting = null;
    this.deleteModal.open = true;
    this.conversationDeleting = conversation;
    this.closeSessionMenu();
  }

  confirmDeleteConversation() {
    this._chatbotService
      .deleteChatbot(this.conversationDeleting.id)
      .pipe(finalize(() => { this.closeDeleteModal(); }))
      .subscribe({
          next: (res) => {
            this._toastService.success("Đã xoá lịch sử chat", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
            this.getConversations(this.workspaceId);
          },
          error: (err) => {
            this._toastService.error("Có lỗi xảy ra khi xoá lịch sử chat!", "Lỗi", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
      });
  }

  closeDeleteModal(): void {
    this.deleteModal.open = false;
  }

  getMessageOnConversation(conversation: any, showLoading = false) {
    this.isFetchingMessage = showLoading;

    this._chatbotService
      .getMessageChatbot(conversation.id)
      .pipe(
        finalize(() => { 
          this.isFetchingMessage = false;
          if (this.messages.length > 0) {
            setTimeout(() => {
              this.scrollToBottom(true, false);
            }, 0);
          }
        })
      )
      .subscribe({
        next: (res: any) => {
          this.messages = res.messages;
        },
        error: (err) => {
          console.error("getMessageChatbot error", err);
        }
      });
  }

  sendMessage(question?: string) {
    if (!this.inputValue.trim() || this.inputValue.trim() === "") {
      if (!question || question.trim() === "") {
        return;
      }
    }

    this.textAskChatbot = this.selection_text;
    // this.isHasTextFromVanBan = false;
    this.selection_text = null;
    this.checkDoneAnswer = false;

    this.messages.push({
      id: new randomUuidv4().randomUuidv4(),
      thinking: "",
      answer: question ? question : this.inputValue.trim(),
      role: "user",
      selection_text: this.textAskChatbot ? this.textAskChatbot : null,
      selected_files: {
        upload_files: this.selectedFile ? this.selectedFile : null,
      },
    });

    setTimeout(() => this.scrollToBottom(), 10);

    this.streamFromChatbot();
    this.inputValue = "";

    this.resetTextareaHeight();
  }

  resetTextareaHeight() {
    const textarea = document.getElementById(
      "queryChatbot"
    ) as HTMLTextAreaElement;
    if (textarea) {
      textarea.style.height = "auto";
    }
  }

  addNewConversation() {
    this.messages = [];
    this.selectedConversation = null;
  }

  clearAnswerTags(answer: string): string {
    return answer
      .replace(/<answer>/g, "")
      .replace(/<\/answer>/g, "");
  }

  clearFileDataState() {
    this.fileData = null;
    this.termIdRef = null;
  }

  onAnswerClick(event: MouseEvent): void {
    this.clearFileDataState();

    let target = event.target as HTMLElement;

    if (target.tagName.toLowerCase() !== "a") {
      target = target.parentNode as HTMLElement;
    }

    if (!target || target.tagName.toLowerCase() !== "a") return;

    const href = target.getAttribute("href");
    if (!href || href.trim() === "") return;

    const parts = href.split(':')[1].split('#');
    if (!parts || parts.length === 0) return;

    const docId = parts.length === 2 ? parts[1] : parts[0];
    const termId = parts.length === 2 ? parts[0] : null;
    if (termId) this.termIdRef = termId;
    // console.log("Clicked link:", href);
    this.showFileReference(docId);
    
    event.preventDefault();
  }

  showFileReference(docId: string): void {
    this._clscChatService.setshowLoadingFile(true);
    this._clscService.setSidebarCollapsed(true);
    this._clscChatService.showFileRef();
    // setTimeout(() => {
    //   this._clscChatService.setshowLoadingFile(false);
    // }, 1000)
    this._clscChatService
      .getDetailFile(docId)
      .pipe(finalize(() => {
        this._clscChatService.setshowLoadingFile(false);
      }))
      .subscribe({
        next: (res) => {
          this.fileData = res;
        },
        error: (err) => {
          console.error("getDetailFile error", err);
        }
      });
  } 

  private cleanModelChunk(chunk: string): string {
    if (!chunk) return "";

    let text = chunk;

    // Bỏ toàn bộ khối <think>...</think> nếu BE có trả về
    text = text.replace(/<think>[\s\S]*?<\/think>/g, "");

    // Bỏ mọi <answer> và </answer> còn sót lại
    text = text.replace(/<answer>/g, "");
    text = text.replace(/<\/answer>/g, "");

    return text;
  }

  sendFromSuggestion(text: string): void {
    if (!this.checkDoneAnswer) return;
    this.sendMessage(text);
  }

  sendFromFollowUp(text: string): void {
    if (!this.checkDoneAnswer) return;
    this.scrollToBottom(true);
    this.sendMessage(text);
  }

  async streamFromChatbot() {
    this.abortController = new AbortController();
    this.bodyMessage = this.messages.map((msg) => ({
      role: msg.role,
      content: msg.answer,
      selection_text: msg.selection_text || null,
      selected_save_files: msg.selected_files.save_files || [],
      selected_upload_files: msg.selected_files.upload_files || [],
    }));

    try {
      let content = null;
      // if (this.textAskChatbot) {
      //   content = {
      //     content: this.textAskChatbot,
      //     original_doc_type:
      //       this.typeDocument == "upload"
      //         ? "uploaded"
      //         : this.typeDocument == "searching"
      //           ? "database"
      //           : this.typeDocument == "search"
      //             ? "saved"
      //             : null,
      //     id_document: this.es_id ? this.es_id : this.fileId || null,
      //   };
      // }
      const response = await fetch(`${environment.apichatbot}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          messages: this.bodyMessage,
          do_rewrite: false,
          stream: true,
          workspace_id: this.workspaceId,
          conversation_id: this.selectedConversation
            ? this.selectedConversation.id
            : null,
          selection_text: content,
          selected_upload_files: this.selected_upload_files,
          selected_save_files: this.selected_save_files,
          use_law_database: this.toolSelected.some((item) => item.value === 1),
          use_google_search: this.toolSelected.some((item) => item.value === 2),
          creative_mode: this.isCreativeMode, // Thêm tham số chế độ sáng tạo
        }),
        signal: this.abortController.signal,
      });

      if (!response.body) {
        console.error("❌ No response body");
        return;
      }
      if (response.ok) {
        if (this.workspaceId) {
          // // console.log("update");
          this._chatbotService.updateTimeWorkspace(this.workspaceId).subscribe();
        }
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let checkStartThinking = false;
      let checkStartAnswer = true;
      let thinkingText = "";
      let answerText = "";
      let id = "";
      this.checkDoneAnswer = false;

      this.scrollToBottom2();
      while (true) {
        if (this.abortController.signal.aborted) {
          this.checkDoneAnswer = true;
          if (this.quota.limit_request - this.quota.request_count > 0) this.quota.request_count += 1;

          break;
        }
        const { value, done } = await reader.read();
        if (done) {
          if (this.quota.limit_request - this.quota.request_count > 0) this.quota.request_count += 1;
          this.checkDoneAnswer = true;

          this.getMessageOnConversation(this.selectedConversation);
          break;
        }
        buffer += decoder.decode(value, { stream: true });

        const [jsonObjects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;

        for (const obj of jsonObjects) {
          this.checkDoneAnswer = false;
          if (obj.status) {
            this.checkDoneThinking = false;
            this.statusThinking = obj.data.message;

            if (obj.status == "need_clarification") {
              this.checkDoneThinking = true;
              this.messages.push({
                id: id,
                thinking: null,
                answer: obj.data.message,
                role: "assistant",
              });
              this.checkDoneAnswer = true;
            }
          } else {
            this.checkDoneThinking = true;
          }

          if (obj.conversation_id && this.selectedConversation == null) {
            this.selectedConversation = {
              id: obj.conversation_id,
            };
            this.getConversations(this.workspaceId);
          }

          if (obj.id) id = obj.id;
          
          const apiTitle = obj.data?.title || obj.doc_title || obj.file_title || null;
          if (apiTitle && id) {
            const msgIdx = this.messages.findIndex((m) => m.id === id);
            if (msgIdx !== -1) {
              this.messages[msgIdx].apiTitle = apiTitle;
            } else {
              this.messages.push({
                id,
                role: "assistant",
                thinking: "",
                answer: "",
                apiTitle,
              });
            }
          }

          let text = "";
          if (obj.text) text = obj.text || "";

          if (this.checkStartThinking(text)) checkStartThinking = true;
          else if (this.checkStartThinking(text) === false) checkStartThinking = false;

          if (checkStartThinking) thinkingText += text;

          if (checkStartAnswer) {
            if (text.includes("<answer>")) {
              answerText += text + "\n\n";
            } else {
              if (answerText === "") {
                answerText += "\n" + text;
              } else {
                answerText += text;
              }
            }
          }

          this.thinkingText = thinkingText;
          this.answerText = answerText;

          const index = this.messages.findIndex((m) => m.id === id);
          if (index !== -1) {
            this.messages[index].thinking = this.thinkingText;
            this.messages[index].answer = this.answerText;
            this.messages[index].role = "assistant";
          } else {
            this.messages.push({
              id: id,
              thinking: this.thinkingText,
              answer: this.answerText,
              role: "assistant",
            });
          }
        }

        this.scrollToBottom(true);
      }
    } catch (error) {
      this.checkDoneAnswer = true;
      this.checkDoneThinking = true;
      // this.toast.error("Thất bại", "Có lỗi xảy ra trong quá trình thực hiện!", {
      //   closeButton: true,
      //   positionClass: "toast-top-right",
      //   toastClass: "toast ngx-toastr",
      // });
    } finally {
      this.abortController = null;
    }
  }

  extractJsonObjects(str: string): any[] {
    const objects: any[] = [];
    let depth = 0;
    let start = -1;
    let inString = false;
    let escape = false;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (char === '"' && !escape) {
        inString = !inString;
      }

      if (!inString) {
        if (char === "{") {
          if (depth === 0) start = i;
          depth++;
        } else if (char === "}") {
          depth--;
          if (depth === 0 && start !== -1) {
            const jsonStr = str.slice(start, i + 1);
            try {
              objects.push(JSON.parse(jsonStr));
            } catch (e) {
              console.warn("⚠️ JSON parse failed", jsonStr);
            }
            start = -1;
          }
        }
      }

      escape = char === "\\" && !escape;
    }

    const remaining = depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }

  cancelChat() {
    this.checkDoneAnswer = true;
    if (this.abortController) {
      this.abortController.abort();
    }
  }

  getQuota() {
    this._chatbotService.getQuota().subscribe((res) => {
      this.quota = {
        limit_request: res.limit_request,
        request_count: res.request_count,
      };
    });
  }

  checkStartThinking(text) {
    if (text.includes("<think>")) return true;
    else if (text.includes("<answer>")) return false;
  }

  checkStartAnswer(text) {
    if (text.includes("<answer>")) return true;
    else if (text.includes("</answer>")) return false;
  }

  handleKeyDownSendMsg(event: KeyboardEvent): void {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  ngOnDestroy(): void {
    if (this.scrollRaf != null) cancelAnimationFrame(this.scrollRaf);
    this._destroy$.next();
    this._destroy$.complete();
  }

  @HostListener("document:click")
  onDocClick(): void {
    if (this.ctxMenu.open) this.closeSessionMenu();
  }

  @HostListener("document:keydown.escape")
  onEsc(): void {
    if (this.ctxMenu.open) this.closeSessionMenu();
    if (this.deleteModal.open) this.closeDeleteModal();
  }

  openSessionMenu(event: MouseEvent, session: any): void {
    event.preventDefault();
    event.stopPropagation();
    // console.log("Open context menu for session:", session);

    // vị trí menu theo chuột
    const padding = 6;
    let x = event.clientX;
    let y = event.clientY;

    // (tuỳ chọn) tránh tràn màn hình
    const menuW = 180;
    const menuH = 96;
    const vw = window.innerWidth;
    const vh = window.innerHeight;

    if (x + menuW > vw) x = vw - menuW - padding;
    if (y + menuH > vh) y = vh - menuH - padding;

    this.ctxMenu = { open: true, x, y, selectConversation: session };
    // console.log("Context menu position:", this.ctxMenu);
  }

  closeSessionMenu(): void {
    this.ctxMenu.open = false;
    this.ctxMenu.selectConversation = null;
  }

  startResize(event: MouseEvent) {
    event.preventDefault();
    this.resizing = true;

    document.addEventListener('mousemove', this.resize);
    document.addEventListener('mouseup', this.stopResize);
    // console.log("Start resizing...");
  }

  resize = (event: MouseEvent) => {
    // console.log("Resizing...", event.clientX);
    if (!this.resizing) return;

    const newWidth = window.innerWidth - event.clientX;

    if (newWidth >= 550 && newWidth <= 800) {
      this.panelWidth = newWidth;
    }
  };

  stopResize = () => {
    // console.log("Stop resizing.");
    this.resizing = false;
    document.removeEventListener('mousemove', this.resize);
    document.removeEventListener('mouseup', this.stopResize);
  };
}
