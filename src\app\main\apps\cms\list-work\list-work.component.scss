@import "@core/scss/angular/libs/select.component.scss";


/* in-flight clone */
.gu-mirror {
  position: fixed !important;
  margin: 0 !important;
  z-index: 9999 !important;
  opacity: 0.8;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  filter: alpha(opacity=80);
  pointer-events: none;
}

/* high-performance display:none; helper */
.gu-hide {
  left: -9999px !important;
}

/* added to mirrorContainer (default = body) while dragging */
.gu-unselectable {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* added to the source element while its mirror is dragged */
.gu-transit {
  opacity: 0.2;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
  filter: alpha(opacity=20);
}

.custom-item-drag-drop {
  cursor: grab;
  border: 1px solid rgba(34, 41, 47, 0.125) !important;
  border-radius: 0.5rem !important;
  background: transparent;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.07), 0 1.5px 3px rgba(0, 0, 0, 0.05);
  // transition: all 0.2s ease-in-out;

  &.selected-work {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%) !important;
    border: 1px solid #008fe3 !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3), 0 2px 6px rgba(33, 150, 243, 0.2) !important;
    transform: translateY(-1px);

    .card {
      background: transparent !important;
    }

    .badge {
      background-color: #008fe3 !important;
      color: white !important;
    }

    h5 {
      color: #008fe3;
      font-weight: 600;
    }
  }

  &:hover:not(.selected-work) {
    background: rgba(0, 0, 0, 0.02) !important;
    border-color: rgba(34, 41, 47, 0.2) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.08) !important;
  }
}

/* Fullscreen for ng-bootstrap modal (BS4/BS5 đều ok) */
.ngb-modal-window.modal-fullscreen {
  padding: 0 !important;
}

.ngb-modal-window.modal-fullscreen .modal-dialog {
  width: 100vw !important;
  max-width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
}

.ngb-modal-window.modal-fullscreen .modal-content {
  height: 100vh !important;
  border-radius: 0 !important;
}

.ngb-modal-window.modal-fullscreen .modal-body {
  overflow: auto;
}

.editor-fs-lock {
  overflow: hidden !important;
}

.editor-wrap {
  position: relative;

  &.fullscreen {
    position: fixed;
    inset: 0;
    z-index: 2050;
    background: #fff;
    padding: 12px;
    display: flex;
    flex-direction: column;
    border-radius: 0;

    .editor-el {
      flex: 1 1 auto;

      .ql-container.ql-snow {
        height: 100%;
      }

      .ql-editor {
        min-height: auto;
        height: calc(100vh - 120px);
      }
    }
  }

  .fs-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0 8px;

    .fs-title {
      font-weight: 600;
      font-size: 1rem;
    }
  }
}

.list-work-container .ql-editor {
  min-height: 220px;
}

.list-work-container .ql-toolbar.ql-snow {
  border-top-left-radius: .357rem;
  border-top-right-radius: .357rem;
}

.list-work-container .ql-container.ql-snow {
  border-bottom-left-radius: .357rem;
  border-bottom-right-radius: .357rem;
}

.icon-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: .2rem .35rem;
  line-height: 1;
  border-radius: .4rem;
}

.icon-btn svg {
  width: 14px;
  height: 14px;
}

.editor-wrap.fullscreen .fs-head .icon-btn {
  padding: .25rem .35rem;
}

.editor-wrap {
  position: relative;
}

.editor-wrap .fs-toggle {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.editor-wrap.fullscreen .fs-toggle {
  top: 10px;
  right: 12px;
}

.ql-toolbar {
  display: flex;
  flex-wrap: wrap;

  .ql-formats-right {
    margin-left: auto;
    display: inline-flex;
    align-items: center;

    button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 28px;
      border: 1px solid transparent;
      border-radius: 4px;
      background: transparent;
      cursor: pointer;

      svg {
        pointer-events: none;
      }

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.editor-wrap {
  position: relative;

  &.fullscreen {
    position: fixed !important;
    inset: 0;
    z-index: 1080;
    background: #fff;
    padding: 12px;

    .editor-el {
      height: calc(100vh - 120px);
    }
  }
}

// .ql-editor .ql-video {
//   width: 100%;
//   height: auto;
//   aspect-ratio: 16 / 9;
// }

// .ql-editor video {
//   max-width: 100%;
//   height: auto;
//   display: block;
// }

// .ql-video-menu {
//   background: #fff;
//   border: 1px solid rgba(0,0,0,.1);
//   border-radius: .375rem;
//   min-width: 180px;
//   box-shadow: 0 8px 24px rgba(0,0,0,.12);
//   padding: .25rem 0;
//   font-size: 14px;
// }

// .ql-video-menu__item {
//   padding: .5rem .75rem;
//   cursor: pointer;
//   white-space: nowrap;
//   line-height: 1.2;
// }

// .ql-video-menu__item:hover {
//   background: #f5f7fa;
// }

/* Video chung – cho nhỏ lại và căn giữa mặc định */
.editor-el .ql-editor .ql-video {
  display: block;
  max-width: 80%;
  /* thay đổi tùy ý, 60–80% sẽ nhìn rõ align hơn */
  height: auto;
  margin: 16px auto;
  /* mặc định giữa */
}

/* Align center */
.editor-el .ql-editor .ql-video.ql-align-center,
.editor-el .ql-editor .ql-align-center .ql-video {
  margin-left: auto;
  margin-right: auto;
}

/* Align right */
.editor-el .ql-editor .ql-video.ql-align-right,
.editor-el .ql-editor .ql-align-right .ql-video {
  margin-left: auto;
  margin-right: 0;
}

/* Align left */
.editor-el .ql-editor .ql-video.ql-align-left,
.editor-el .ql-editor .ql-align-left .ql-video {
  margin-left: 0;
  margin-right: auto;
}

.thumb-uploader {
  display: block;
}

.thumb-preview {
  position: relative;
  width: 100%;
  max-width: 460px;
}

.thumb-preview img {
  width: 100%;
  aspect-ratio: 16 / 9;
  object-fit: cover;
  border-radius: 12px;
  display: block;
}

.thumb-actions {
  margin-top: .5rem;
  display: flex;
  gap: .5rem;
}

.ql-editor table {
  border-collapse: collapse;
  width: 100%;
}

.ql-editor table td,
.ql-editor table th {
  border: 1px solid #e5e7eb;
  padding: 6px 8px;
}

.ql-editor table th {
  background: #f9fafb;
  font-weight: 600;
}

quill-editor.is-invalid .ql-toolbar.ql-snow,
quill-editor.is-invalid .ql-container.ql-snow {
  border-color: #ea5455 !important;
  box-shadow: none !important;
}

.kanban-col {
  transition: filter .15s ease, opacity .15s ease, background-color .15s ease;
  border-radius: 10px;

  &.drop-allowed {
    background: rgba(14, 165, 233, .08);
    outline: 2px dashed rgba(14, 165, 233, .5);
    outline-offset: 4px;
  }

  &.drop-blocked {
    filter: grayscale(.6);
    opacity: .5;
  }
}

.kanban-col {
  position: relative;
  transition: filter .15s ease, opacity .15s ease, background-color .15s ease;
  border-radius: 10px;

  &.drop-allowed {
    background: rgba(14, 165, 233, .08);
    outline: 2px dashed rgba(14, 165, 233, .45);
    outline-offset: 4px;
  }

  &.drop-blocked {
    filter: grayscale(.6);
    opacity: .5;
  }

  .kanban-col__chips {
    position: absolute;
    top: 6px;
    left: 8px;
    right: 8px;
    display: flex;
    justify-content: center;
    gap: 8px;
    pointer-events: none;
    z-index: 2;
  }

  .kanban-chip {
    font-size: 11px;
    font-weight: 600;
    letter-spacing: .3px;
    text-transform: uppercase;
    padding: 6px 10px;
    border-radius: 999px;
    border: 1px solid rgba(2, 18, 43, .15);
    background: #eef6ff;
    box-shadow: 0 1px 0 rgba(2, 18, 43, .05);
  }
}

body.kanban-dragging .draggable>.list-group-item:not(.gu-transit):not(.gu-mirror) {
  opacity: 0;
  pointer-events: none;
}

.kanban-col.drop-allowed {
  filter: none;
}

.kanban-col.drop-blocked {
  filter: grayscale(1) opacity(.35);
}

.kanban-intent {
  position: sticky;
  top: 8px;
  z-index: 5;
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.hide-on-drag {
  display: none !important;
}

.kanban-chip-wrap {
  position: sticky;
  top: 8px;
  z-index: 5;
  display: none;
  justify-content: center;
  pointer-events: none;
  margin-bottom: 8px;

  &.visible {
    display: flex;
  }

  &.visible .kanban-chip {
    box-shadow: 0 1px 2px rgba(0, 0, 0, .06), inset 0 0 0 2px rgba(37, 99, 235, .10);
    border-color: #c7d2fe;
    /* indigo-200 */
  }

  /* cột nguồn (đang kéo từ) */
  &.is-source .kanban-chip {
    border-style: dashed;
    opacity: .95;
  }

  /* đang hover lên cột nào */
  &.is-hover .kanban-chip {
    background: #eef2ff;
    /* indigo-50 */
    border-color: #818cf8;
    /* indigo-400 */
    transform: translateY(-1px);
  }
}

/* Hình dạng chip */
.kanban-chip {
  font-size: 12px;
  line-height: 1;
  padding: 8px 12px;
  border-radius: 999px;
  background: #ffffff;
  color: #111827;
  border: 1px solid #e5e7eb;
  /* gray-200 */
  font-weight: 700;
  letter-spacing: .3px;
  text-transform: uppercase;
  white-space: nowrap;
  transition: .15s ease;
}

.kanban-chip.blocked {
  background: rgba(0, 0, 0, .04);
  border-color: rgba(0, 0, 0, .12);
  color: #8a8a8a;
}

.cms-editor-modal .modal-body {
  position: relative;
}

.cms-editor-modal .modal-body .ql-toolbar.ql-snow {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #fff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 6px 16px rgba(2, 18, 43, .08);
  margin: 0 !important;
}

.ng-select .ng-option.ng-option-marked,
.ng-select .ng-option.ng-option-selected {
  // background-color: #008fe3 !important;
  /* nền xanh */
}

// .ng-select .ng-option.ng-option-marked .ng-option-label,
.ng-select .ng-option.ng-option-selected .ng-option-label {
  color: #fff !important;
  /* chữ trắng */
}

// .ng-select.ng-select-multiple
//   .ng-select-container .ng-value {
//   background-color: #008fe3 !important;
//   border-color: #008fe3 !important;
// }

.ng-select.ng-select-multiple .ng-select-container .ng-value .ng-value-label,
.ng-select.ng-select-multiple .ng-select-container .ng-value .ng-value-icon {
  color: #fff !important;
}

.ngb-dp-header {
  background: #fff !important;
}

.ngb-dp-weekdays {
  background: #fff !important;
}

.btn-light {
  color: black
}