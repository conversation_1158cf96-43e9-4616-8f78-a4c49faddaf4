import { Component, Input, ChangeDetectionStrategy } from '@angular/core';

export type LoadingMode = 'default' | 'overlay' | 'fullscreen';

@Component({
  selector: 'app-commercial-loading',
  templateUrl: './commercial-loading.component.html',
  styleUrls: ['./commercial-loading.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CommercialLoadingComponent {
  // Text
  @Input() text: string = null;
  @Input() color: string = '#000';
  @Input() size: string = null;
  @Input() bold: boolean = false;

  // Spinner
  @Input() showSpinner: boolean = true;
  @Input() spinnerSize: string = null;
  @Input() spinnerBW: string = null;

  // SVG
  @Input() width: string = null;
  @Input() height: string = null;

  // Mode
  @Input() mode: LoadingMode = 'default';
  @Input() pulse: boolean = false;

  // Visibility
  @Input() visible: boolean = true;
}