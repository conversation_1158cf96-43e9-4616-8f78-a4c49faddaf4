<header class="header sc-header d-flex align-items-center justify-content-between" [class.scrolled]="scrolled">
  <div class="sc-header-left d-flex align-items-center cursor-pointer" (click)="toLandingPage()">
    <img [src]="logoPath" alt="Logo" class="sc-header-logo" />
    <span class="sc-header-app-name text-primary-theme">{{ appName }}</span>
  </div>

  <div class="sc-header-right">
    <span *ngIf="!currentUser" class="sc-login-btn badge badge-pill badge-light-primary cursor-pointer" (click)="toLoginPage()">
      Đăng nhập
    </span>
    <span *ngIf="currentUser" class="sc-login-btn badge badge-pill badge-primary cursor-pointer" (click)="toLoginPage()">
      Trải nghiệm ngay
    </span>
    
    <!-- <ul *ngIf="currentUser" class="nav navbar-nav align-items-center ml-auto">
      <li ngbDropdown class="nav-item dropdown-user">
        <a
          class="mr-2 nav-link dropdown-user-link d-flex"
          id="dropdown-user"
          ngbDropdownToggle
          id="navbarUserDropdown"
          aria-haspopup="true"
          aria-expanded="false"
        >
          <div class="user-nav d-none d-flex flex-column mr-1">
            <span class="user-name font-weight-bolder">{{
              userName == "" ? "CLS User" : userName
            }}</span>
            <span class="user-status text-right">{{
              role === "ADMIN"
                ? "ADMIN"
                : role === "SUPER_ADMIN"
                ? "SUPER_ADMIN"
                : "USER"
            }}</span>
          </div>

          <span class="avatar"
            ><img
              class="round"
              [src]="
                currentUser.avatar
                  ? currentUser.avatar
                  : 'assets/images/portrait/small/users.png'
              "
              (error)="refreshLinkAvatar($event)"
              alt="avatar"
              height="40"
              width="40" /><span class="avatar-status-online"></span
          ></span>
        </a>
        <div
          ngbDropdownMenu
          aria-labelledby="navbarUserDropdown"
          class="detail-work-space-dropdown dropdown-menu dropdown-menu-right"
        >
          <a ngbDropdownItem (click)="editUser()">
            <span [data-feather]="'user'" class="mr-50"></span> Thông tin
          </a>
          <a
            ngbDropdownItem
            (click)="addAccount()"
            *ngIf="role === 'ADMIN' || role == 'SUPER_ADMIN'"
          >
            <span [data-feather]="'user-plus'" class="mr-50"></span>
            {{ "AddAccount" | translate }}
          </a>
          <a ngbDropdownItem (click)="changePass()" *ngIf="!isADUser">
            <span [data-feather]="'lock'" class="mr-50"></span> Đổi mật khẩu
          </a>
          <a
            *ngIf="role == 'SUPER_ADMIN'"
            ngbDropdownItem
            [routerLink]="['/cau-hinh']"
          >
            <span [data-feather]="'settings'" class="mr-50"></span> Cấu hình
          </a>
          <a ngbDropdownItem (click)="report()">
            <span [data-feather]="'alert-triangle'" class="mr-50"></span> Báo cáo
          </a>
          <a
            ngbDropdownItem
            [routerLink]="['/pages/authentication/login-v2']"
            (click)="logout()"
          >
            <span [data-feather]="'power'" class="mr-50"></span> Đăng xuất
          </a>
        </div>
      </li>
    </ul> -->
  </div>
</header>
