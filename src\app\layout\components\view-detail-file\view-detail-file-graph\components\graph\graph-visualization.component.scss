.graph-content {
  /* Default height for detail view; can be overridden via CSS variable */
  --graph-height: calc(100vh - 12rem);

  border: 1px solid #e0e0e0;
  border-radius: 8px;
  height: var(--graph-height);
  max-height: 100vh;
  min-height: 320px;
  background: #fafafa;
  padding: 10px 10px 0 10px;
  transition: filter 0.2s ease;

  &.is-loading {
    filter: blur(3px);
    pointer-events: none;
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}

/* Full-height variant for Tìm kiếm đồ thị modal */
.graph-content--timkiem {
  --graph-height: 86vh;
}