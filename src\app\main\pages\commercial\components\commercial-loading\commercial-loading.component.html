<ng-container *ngIf="visible">
  <div
    class="clsc-loading-wrapper h-100 mb-5"
    [class.clsc-loading-overlay]="mode === 'overlay' || mode === 'fullscreen'"
    [class.clsc-loading-fullscreen]="mode === 'fullscreen'"
  >
    <div class="clsc-loading-content" [class.clsc-loading-pulse]="pulse">
      <div class="clsc-loading-icon">
        <img 
          src="assets/images/logo/COpenAIlogo.svg"
          alt="loading"
          [ngStyle]="{
            'width.px': width ? width : '', 
            'height.px': height ? height : '' 
          }"
        />
      </div>

      <div *ngIf="showSpinner" class="clsc-loading-spinner mb-1">
        <div 
          class="spinner-border text-primary" 
          role="status"
          [ngStyle]="{ 
            'width.rem': spinnerSize ? spinnerSize : '', 
            'height.rem': spinnerSize ? spinnerSize : '',
            'borderWidth.rem': spinnerBW ? spinnerBW : ''
          }"
        >
          <span class="sr-only">Loading...</span>
        </div>
      </div>

      <p
        *ngIf="text"
        class="clsc-loading-text"
        [ngStyle]="{ 
          'color': color,
          'fontSize.px': size ? size : '',
          'fontWeight': bold ? 'bold' : 'normal'
        }"
      >
        {{ text }}
      </p>
    </div>
  </div>
</ng-container>