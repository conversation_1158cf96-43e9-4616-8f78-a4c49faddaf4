import { Component, Input, OnInit, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { SharedConversationService } from '../../shared-conversation.service';
import {
  getLogoImage as getLogoImageHelper,
  getApp<PERSON><PERSON> as getAppNameHelper,
  isVP<PERSON><PERSON><PERSON><PERSON>,
  VPQH_COLOR_CODE,
} from "app/shared/image.helper";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";

@Component({
  selector: 'app-shared-conversation-footer',
  templateUrl: './shared-conversation-footer.component.html',
  styleUrls: ['./shared-conversation-footer.component.scss']
})
export class SharedConversationFooterComponent implements OnInit {

  constructor(

  ) {}

  ngOnInit(): void {

  }

}
