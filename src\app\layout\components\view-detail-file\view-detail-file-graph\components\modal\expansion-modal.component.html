<div class="modal-backdrop" *ngIf="visible" (click)="onClose()"></div>
<div class="expansion-modal" *ngIf="visible" (click)="onModalClick($event)">
  <div class="modal-header">
    <h5 class="modal-title">Mở rộng theo điều kiện</h5>
    <button type="button" class="close-btn" (click)="onClose()" aria-label="Đóng">
      &times;
    </button>
  </div>
  <div class="modal-body">
    <form class="form form-vertical" *ngIf="formState">
      <div class="row">
        <!-- Row 1: Mở rộng với -->
        <div class="col-12">
          <div class="form-group">
            <label class="col-form-label font-weight-bold">Mở rộng với</label>
            <div class="btn-group btn-group-toggle d-flex" ngbRadioGroup name="modalRadioBasic" [(ngModel)]="formState.search_legal_term">
              <label ngbButtonLabel class="btn-outline-primary-theme btn-sm flex-fill" rippleEffect>
                <input ngbButton type="radio" value="VAN_BAN" /> Văn bản
              </label>
              <label ngbButtonLabel class="btn-outline-primary-theme btn-sm flex-fill" rippleEffect>
                <input ngbButton type="radio" value="DIEU_KHOAN" /> Điều khoản
              </label>
              <label ngbButtonLabel class="btn-outline-primary-theme btn-sm flex-fill" rippleEffect>
                <input ngbButton type="radio" value="ALL" /> Văn bản & Điều khoản
              </label>
            </div>
          </div>
        </div>

        <!-- Row 2: Mối quan hệ với tài liệu gốc -->
        <div class="col-6">
          <div class="form-group">
            <label class="col-form-label font-weight-bold">
              Mối quan hệ với tài liệu gốc<span *ngIf="moiQuanHeCheckedCount > 0" class="filter-count"> ({{ moiQuanHeCheckedCount }})</span>
            </label>
            <app-search-checkbox-list
              [options]="boLocMoiQuanHeOptions"
              [selectedValues]="formState.selectedBoLocMoiQuanHe"
              [idPrefix]="'modal-moiquanhe'"
              [searchPlaceholder]="'Tìm kiếm'"
              [maxHeight]="120"
              (selectionChange)="formState.selectedBoLocMoiQuanHe = $event"
            ></app-search-checkbox-list>
          </div>
        </div>

        <!-- Row 2: Cơ quan ban hành -->
        <div class="col-6">
          <div class="form-group">
            <label class="col-form-label font-weight-bold">
              Cơ quan ban hành<span *ngIf="coQuanBanHanhCheckedCount > 0" class="filter-count"> ({{ coQuanBanHanhCheckedCount }})</span>
            </label>
            <app-search-checkbox-list
              [options]="coQuanBanHanhOptions"
              [selectedValues]="formState.selectedCoQuanBanHanh"
              [idPrefix]="'modal-coquanbanhanh'"
              [searchPlaceholder]="'Tìm kiếm'"
              [maxHeight]="120"
              (selectionChange)="formState.selectedCoQuanBanHanh = $event"
            ></app-search-checkbox-list>
          </div>
        </div>

        <!-- Row 3: Loại văn bản -->
        <div class="col-6">
          <div class="form-group">
            <label class="col-form-label font-weight-bold">
              Loại văn bản<span *ngIf="loaiVanBanCheckedCount > 0" class="filter-count"> ({{ loaiVanBanCheckedCount }})</span>
            </label>
            <app-search-checkbox-list
              [options]="boLocLoaiVanBanOptions"
              [selectedValues]="formState.selectedBoLocLoaiVanBan"
              [idPrefix]="'modal-loaivanban'"
              [searchPlaceholder]="'Tìm kiếm'"
              [maxHeight]="120"
              (selectionChange)="formState.selectedBoLocLoaiVanBan = $event"
            ></app-search-checkbox-list>
          </div>
        </div>

        <!-- Row 3: Trạng thái hiệu lực -->
        <div class="col-6">
          <div class="form-group">
            <label class="col-form-label font-weight-bold">
              Trạng thái hiệu lực<span *ngIf="trangThaiHieuLucCheckedCount > 0" class="filter-count"> ({{ trangThaiHieuLucCheckedCount }})</span>
            </label>
            <app-search-checkbox-list
              [options]="tinhTrangHieuLucOptions"
              [selectedValues]="formState.selectedTinhTrangHieuLuc"
              [idPrefix]="'modal-trangthaihieuluc'"
              [searchPlaceholder]="'Tìm kiếm'"
              [maxHeight]="120"
              (selectionChange)="formState.selectedTinhTrangHieuLuc = $event"
            ></app-search-checkbox-list>
          </div>
        </div>

        <!-- Row 4: Lọc theo thời gian -->
        <div class="col-6">
          <div class="form-group">
            <label class="col-form-label font-weight-bold">Lọc theo thời gian</label>
            <div class="mb-2">
              <div class="form-check form-check-inline d-flex align-items-center mb-50">
                <input
                  type="radio"
                  class="form-check-input"
                  id="modal-date-filter-ban-hanh"
                  name="modal-date-filter-mode"
                  [checked]="formState?.dateFilterMode === 'ban_hanh'"
                  (click)="onDateFilterModeChange('ban_hanh', $event)"
                />
                <label class="form-check-label mb-0" for="modal-date-filter-ban-hanh">
                  Lọc theo thời gian ban hành
                </label>
              </div>
              <div class="form-check form-check-inline d-flex align-items-center">
                <input
                  type="radio"
                  class="form-check-input"
                  id="modal-date-filter-hieu-luc"
                  name="modal-date-filter-mode"
                  [checked]="formState?.dateFilterMode === 'hieu_luc'"
                  (click)="onDateFilterModeChange('hieu_luc', $event)"
                />
                <label class="form-check-label mb-0" for="modal-date-filter-hieu-luc">
                  Lọc theo thời gian có hiệu lực
                </label>
              </div>
            </div>
            <ng2-flatpickr
              *ngIf="isDateFilterModeSelected"
              #modalYearPicker
              [config]="customYearOptions"
              placeholder="Chọn khoảng thời gian"
              name="modalYear"
              (change)="onYearChange($event)"
            ></ng2-flatpickr>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary-theme"
      rippleEffect
      (click)="onSubmit()"
    >
      Lưu
    </button>
  </div>
</div>

