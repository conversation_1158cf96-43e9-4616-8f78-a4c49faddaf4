import { Component, Input, Output, EventEmitter, ChangeDetectorRef, OnChanges, SimpleChanges, ViewChild } from '@angular/core';
import { NouisliderComponent } from 'ng2-nouislider';
import { GraphFormState } from '../../types/graph.types';
import { ApiNode } from '../../types/graph.types';
import { LOAI_VAN_BAN_COLORS } from '../../constants/graph.constants';

export interface DocumentListItem {
  id: string;
  title: string;
  selected: boolean;
  apiNode: ApiNode;
}

@Component({
  selector: 'app-document-list-panel',
  templateUrl: './document-list-panel.component.html',
  styleUrls: ['./document-list-panel.component.scss'],
})
export class DocumentListPanelComponent implements OnChanges {
  @Input() expanded: boolean = false;
  @Input() documentList: DocumentListItem[] = [];
  @Input() documentListSearch: string = '';
  @Input() selectAllDocuments: boolean = false;
  @Input() highlightedDocumentId: string | null = null;
  @Input() activeDocumentTab: string = 'timkiem';
  @Input() formState: GraphFormState | null = null;
  @Input() boLocMoiQuanHeOptions: any[] = [];
  @Input() coQuanBanHanhOptions: any[] = [];
  @Input() boLocLoaiVanBanOptions: any[] = [];
  @Input() tinhTrangHieuLucOptions: any[] = [];
  loaiVanBanColorMap = LOAI_VAN_BAN_COLORS;

  private readonly DEFAULT_MIN_YEAR = 1900;
  private readonly DEFAULT_MAX_YEAR = 2025;

  private yearRanges = {
    ban_hanh: { min: this.DEFAULT_MIN_YEAR, max: this.DEFAULT_MAX_YEAR },
    hieu_luc: { min: this.DEFAULT_MIN_YEAR, max: this.DEFAULT_MAX_YEAR },
  };

  yearSliderValue: number[] = [this.DEFAULT_MIN_YEAR, this.DEFAULT_MAX_YEAR];
  yearSliderConfig: any = {
    behaviour: 'drag',
    connect: true,
    step: 1,
    range: { min: this.DEFAULT_MIN_YEAR, max: this.DEFAULT_MAX_YEAR },
    tooltips: [
      {
        to: (value: number) => Math.round(value).toString(),
        from: (value: string) => Number(value),
      },
      {
        to: (value: number) => Math.round(value).toString(),
        from: (value: string) => Number(value),
      },
    ],
  };
 
  constructor(private cdr: ChangeDetectorRef) {}
 
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['documentList']) {
      this.recalculateYearRanges();
      this.syncSliderWithCurrentMode(this.formState?.dateFilterMode);
    }

    if (changes['formState'] && !changes['formState'].firstChange) {
      const prevMode = changes['formState'].previousValue?.dateFilterMode;
      const nextMode = changes['formState'].currentValue?.dateFilterMode;
      if (prevMode !== nextMode) {
        this.syncSliderWithCurrentMode(nextMode);
      }
    }
  }

  @Output() toggle = new EventEmitter<void>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() selectAllChange = new EventEmitter<boolean>();
  @Output() documentSelectChange = new EventEmitter<{ item: DocumentListItem; selected: boolean }>();
  @Output() toggleFullTable = new EventEmitter<void>();
  @Output() viewModeChange = new EventEmitter<string>();
  @Output() moiQuanHeSelectionChange = new EventEmitter<string[]>();
  @Output() coQuanBanHanhSelectionChange = new EventEmitter<string[]>();
  @Output() loaiVanBanSelectionChange = new EventEmitter<string[]>();
  @Output() trangThaiHieuLucSelectionChange = new EventEmitter<string[]>();
  @Output() yearChange = new EventEmitter<any>();
  @Output() dateFilterModeChange = new EventEmitter<{ mode: 'ban_hanh' | 'hieu_luc' | null; event: Event }>();
  @Output() activeTabChange = new EventEmitter<string>();
  @Output() documentHover = new EventEmitter<string | null>();

  @ViewChild('yearSlider', { static: false }) yearSlider?: NouisliderComponent;

  getFilteredDocumentList(): DocumentListItem[] {
    const query = (this.documentListSearch || '').toLowerCase().trim();
    if (!query) {
      return this.documentList;
    }
    return this.documentList.filter((item) => {
      const normalizedTitle = (item.title || '').toLowerCase();
      return normalizedTitle.includes(query);
    });
  }

  /**
   * Extract year from ISO date string (e.g., "2023-01-09T00:00:00Z" -> 2023)
   */
  private extractYear(dateString: string | null | undefined): number | null {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return null;
      return date.getFullYear();
    } catch {
      return null;
    }
  }

  /**
   * Build year histogram buckets from real document data
   * Based on the selected date filter mode (ban_hanh or hieu_luc)
   */
  get yearBuckets(): Array<{ year: number; value: number }> {
    const mode = this.formState?.dateFilterMode;
    const [minYear, maxYear] = this.getYearRangeForMode(mode);

    // Count occurrences for each year in the dynamic range
    const yearCounts = new Map<number, number>();
    for (let year = minYear; year <= maxYear; year++) {
      yearCounts.set(year, 0);
    }

    // Extract and count years based on the selected mode
    this.documentList.forEach((item) => {
      const thuocTinh = item.apiNode?.thuoc_tinh;
      if (!thuocTinh) return;

      let year: number | null = null;
      if (mode === 'ban_hanh') {
        year = this.extractYear(thuocTinh.ngay_ban_hanh);
      } else if (mode === 'hieu_luc') {
        year = this.extractYear(thuocTinh.ngay_co_hieu_luc);
      }

      if (year !== null && year >= minYear && year <= maxYear) {
        const currentCount = yearCounts.get(year) || 0;
        yearCounts.set(year, currentCount + 1);
      }
    });

    // Convert map to array of buckets
    return Array.from(yearCounts.entries())
      .map(([year, value]) => ({ year, value }))
      .sort((a, b) => a.year - b.year);
  }

  /**
   * Get the maximum value from year buckets for normalization
   */
  get maxYearValue(): number {
    return this.yearBuckets.reduce((max, bucket) => Math.max(max, bucket.value), 1);
  }

  /**
   * Normalized year buckets with height percentages for histogram display
   */
  get normalizedYearBuckets(): Array<{ year: number; value: number; height: number }> {
    const maxValue = this.maxYearValue;
    return this.yearBuckets.map((bucket) => ({
      ...bucket,
      height: Math.round((bucket.value / maxValue) * 100),
    }));
  }

  /**
   * Checked-count helpers for quick filter headers.
   * We only show counts when at least one option is checked.
   */
  get moiQuanHeCheckedCount(): number {
    return this.formState?.selectedBoLocMoiQuanHe?.length || 0;
  }

  get coQuanBanHanhCheckedCount(): number {
    return this.formState?.selectedCoQuanBanHanh?.length || 0;
  }

  get loaiVanBanCheckedCount(): number {
    return this.formState?.selectedBoLocLoaiVanBan?.length || 0;
  }

  get trangThaiHieuLucCheckedCount(): number {
    return this.formState?.selectedTinhTrangHieuLuc?.length || 0;
  }


  onSearchChange(value: string): void {
    this.documentListSearch = value;
    this.searchChange.emit(value);
  }

  onSelectAllChange(checked: boolean): void {
    this.selectAllChange.emit(checked);
  }

  onDocumentSelectChange(item: DocumentListItem, checked: boolean): void {
    this.documentSelectChange.emit({ item, selected: checked });
  }

  onToggle(): void {
    this.toggle.emit();
  }

  onToggleFullTable(): void {
    this.toggleFullTable.emit();
  }

  onViewModeChange(value: string): void {
    this.viewModeChange.emit(value);
  }

  onMoiQuanHeChange(values: string[]): void {
    this.moiQuanHeSelectionChange.emit(values);
  }

  onCoQuanBanHanhChange(values: string[]): void {
    this.coQuanBanHanhSelectionChange.emit(values);
  }

  onLoaiVanBanChange(values: string[]): void {
    this.loaiVanBanSelectionChange.emit(values);
  }

  onTrangThaiHieuLucChange(values: string[]): void {
    this.trangThaiHieuLucSelectionChange.emit(values);
  }


  /**
   * During dragging (update), avoid feeding value back to prevent push-pull.
   * Keep the ngModel two-way binding for live UI update, but do NOTHING here.
   */
  private lastEmittedYearRange: [number, number] | null = null;

  /**
   * Compute dynamic min/max years for ban_hanh and hieu_luc modes from documentList.
   * Falls back to default bounds if data is missing.
   */
  private recalculateYearRanges(): void {
    let minBanHanh = Number.POSITIVE_INFINITY;
    let maxBanHanh = Number.NEGATIVE_INFINITY;
    let minHieuLuc = Number.POSITIVE_INFINITY;
    let maxHieuLuc = Number.NEGATIVE_INFINITY;

    this.documentList.forEach((item) => {
      const thuocTinh = item.apiNode?.thuoc_tinh;
      if (!thuocTinh) return;

      const banHanhYear = this.extractYear(thuocTinh.ngay_ban_hanh);
      if (banHanhYear !== null) {
        minBanHanh = Math.min(minBanHanh, banHanhYear);
        maxBanHanh = Math.max(maxBanHanh, banHanhYear);
      }

      const hieuLucYear = this.extractYear(thuocTinh.ngay_co_hieu_luc);
      if (hieuLucYear !== null) {
        minHieuLuc = Math.min(minHieuLuc, hieuLucYear);
        maxHieuLuc = Math.max(maxHieuLuc, hieuLucYear);
      }
    });

    if (!isFinite(minBanHanh) || !isFinite(maxBanHanh)) {
      minBanHanh = this.DEFAULT_MIN_YEAR;
      maxBanHanh = this.DEFAULT_MAX_YEAR;
    }

    if (!isFinite(minHieuLuc) || !isFinite(maxHieuLuc)) {
      minHieuLuc = this.DEFAULT_MIN_YEAR;
      maxHieuLuc = this.DEFAULT_MAX_YEAR;
    }

    this.yearRanges = {
      ban_hanh: { min: minBanHanh, max: maxBanHanh },
      hieu_luc: { min: minHieuLuc, max: maxHieuLuc },
    };
  }

  /**
   * Get min/max years for the given mode with sensible fallbacks.
   */
  private getYearRangeForMode(mode: 'ban_hanh' | 'hieu_luc' | null | undefined): [number, number] {
    if (mode === 'hieu_luc') {
      return [this.yearRanges.hieu_luc.min, this.yearRanges.hieu_luc.max];
    }
    // Default to ban_hanh range
    return [this.yearRanges.ban_hanh.min, this.yearRanges.ban_hanh.max];
  }

  /**
   * Update slider bounds and handles to match the current mode’s data-driven range.
   */
  private syncSliderWithCurrentMode(mode: 'ban_hanh' | 'hieu_luc' | null | undefined): void {
    const [minYear, maxYear] = this.getYearRangeForMode(mode);

    this.yearSliderConfig = {
      ...this.yearSliderConfig,
      range: { ...this.yearSliderConfig.range, min: minYear, max: maxYear },
    };

    this.yearSliderValue = [minYear, maxYear];
    this.lastEmittedYearRange = null;

    // If the slider is already rendered, push the new range to nouislider
    if (this.yearSlider?.slider) {
      this.yearSlider.slider.updateOptions(
        { range: { min: minYear, max: maxYear } },
        false
      );
      this.yearSlider.slider.set([minYear, maxYear]);
    }

    this.cdr.markForCheck();
  }

  /**
   * Called continuously while dragging the slider (via ngModelChange).
   * Emits filtering events in real-time for smooth visual feedback.
   */
  onYearSliderChange(value: any): void {
    if (!Array.isArray(value) || value.length !== 2 || !this.formState?.dateFilterMode) {
      return;
    }

    const v0 = Math.round(Number(value[0] || 0));
    const v1 = Math.round(Number(value[1] || 0));

    // Skip if the rounded year range hasn't changed
    if (this.lastEmittedYearRange?.[0] === v0 && this.lastEmittedYearRange?.[1] === v1) {
      return;
    }

    this.lastEmittedYearRange = [v0, v1];

    // Convert year range to full date range and emit
    const fromDate = new Date(v0, 0, 1);
    const toDate = new Date(v1, 11, 31, 23, 59, 59, 999);

    this.yearChange.emit({
      target: {
        value: `${fromDate.toISOString().slice(0, 10)} to ${toDate.toISOString().slice(0, 10)}`,
      },
    } as any);
  }

  /**
   * After user releases handle ('change' event), round values and update state once.
   * Also triggers filtering by emitting yearChange event with date range.
   */
  onYearSliderFinal(value: any): void {
    if (!Array.isArray(value) || value.length !== 2) {
      return;
    }

    const v0 = Math.round(Number(value[0] || 0));
    const v1 = Math.round(Number(value[1] || 0));

    // Mutate the existing array to avoid churn / re-creation which can confuse nouislider
    let changed = false;
    if (v0 !== this.yearSliderValue[0]) {
      this.yearSliderValue[0] = v0;
      changed = true;
    }
    if (v1 !== this.yearSliderValue[1]) {
      this.yearSliderValue[1] = v1;
      changed = true;
    }

    if (changed) {
      // Kick Angular change detection manually since we mutated in place
      this.cdr.markForCheck();

      // Only emit yearChange if dateFilterMode is selected
      // Convert year range to date range (start of first year to end of last year)
      if (this.formState?.dateFilterMode) {
        const fromDate = new Date(v0, 0, 1); // January 1st of first year
        const toDate = new Date(v1, 11, 31, 23, 59, 59, 999); // December 31st of last year
        
        // Format dates as YYYY-MM-DD for flatpickr compatibility
        const fromDateStr = fromDate.toISOString().slice(0, 10);
        const toDateStr = toDate.toISOString().slice(0, 10);
        
        // Create a fake event object that mimics flatpickr's event format
        const fakeEvent = {
          target: {
            value: `${fromDateStr} to ${toDateStr}`
          }
        } as any;
        
        // Emit the event to trigger filtering
        this.yearChange.emit(fakeEvent);
      }
    }
  }

  onDateFilterModeChange(mode: 'ban_hanh' | 'hieu_luc', event: Event): void {
    this.dateFilterModeChange.emit({ mode, event });
    this.syncSliderWithCurrentMode(mode);
    
    // If switching to a mode and we have a year range selected, apply it immediately
    // Use setTimeout to ensure formState is updated first by the parent
    setTimeout(() => {
      if (this.formState?.dateFilterMode && this.yearSliderValue.length === 2) {
        const v0 = this.yearSliderValue[0];
        const v1 = this.yearSliderValue[1];
        
        const fromDate = new Date(v0, 0, 1); // January 1st of first year
        const toDate = new Date(v1, 11, 31, 23, 59, 59, 999); // December 31st of last year
        
        // Format dates as YYYY-MM-DD for flatpickr compatibility
        const fromDateStr = fromDate.toISOString().slice(0, 10);
        const toDateStr = toDate.toISOString().slice(0, 10);
        
        // Create a fake event object that mimics flatpickr's event format
        const fakeEvent = {
          target: {
            value: `${fromDateStr} to ${toDateStr}`
          }
        } as any;
        
        // Emit the event to trigger filtering with the new mode
        this.yearChange.emit(fakeEvent);
      }
    }, 0);
  }

  onActiveTabChange(tabId: string): void {
    this.activeTabChange.emit(tabId);
  }

  onDocumentHover(documentId: string): void {
    this.documentHover.emit(documentId);
  }

  onDocumentHoverLeave(): void {
    this.documentHover.emit(null);
  }
}

