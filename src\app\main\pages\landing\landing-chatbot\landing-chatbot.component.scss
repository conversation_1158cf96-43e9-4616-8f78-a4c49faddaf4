/* ========== Chatbot base ========== */
:host {
  display: block;
  width: 100%;
}

:root {
  --cb-min: 520px;
  --cb-fluid: 68vw;
  --cb-max: 920px;
}

.chatbot__title br + * {
  white-space: normal;
}


.chatbot__title br + * {
  white-space: normal;
}


@keyframes cb-title-wave {
  0%   { background-position:   0% 0; }
  50%  { background-position: 100% 0; }
  100% { background-position:   0% 0; }
}


.chatbot__search::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  pointer-events: none;

  background: linear-gradient(90deg, #2D8DFE, #B8B6C8, #F7D44A);
  -webkit-mask:
    linear-gradient(#000 0 0) content-box,
    linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

@keyframes cb-border-soft {
  0%   { background-position:   0% 0; }
  50%  { background-position: 100% 0; }
  100% { background-position:   0% 0; }
}

.chatbot__input:focus {
  border-color: #b8c8ff;
}

.chatbot__send {
  position: absolute;
  right: 18px;
  bottom: 20px;

  width: 34px;
  height: 34px;

  border-radius: 50%;
  border: 1px solid #d1d5db;

  background: #ffffff;
  color: #9ca3af;

  display: grid;
  place-items: center;

  cursor: pointer;
  transition: 0.15s ease;
}

.chatbot__send:hover {
  background: #f3f4f6;
}


.chatbot__send:hover:not(:disabled) {
  background: #E5E7EB;
  color: #111827;
  box-shadow: 0 4px 10px rgba(15, 23, 42, .15);
}

.chatbot__send:active:not(:disabled) {
  transform: translateY(1px);
}

.chatbot__send:disabled {
  opacity: .5;
  cursor: default;
  box-shadow: none;
}


.chatbot__send:hover:not(:disabled) {
  background: #E5E7EB;
  color: #111827;
  box-shadow: 0 4px 10px rgba(15, 23, 42, .15);
}

.chatbot__send:active:not(:disabled) {
  transform: translateY(1px);
}

.chatbot__send:disabled {
  opacity: .5;
  cursor: default;
  box-shadow: none;
}

/* ===== Chips gợi ý ===== */
.chatbot__chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 6px;
}

.chip {
  padding: 8px 16px;
  border-radius: 999px;
  border: 1px solid rgba(148, 163, 184, .4);
  background: #ffffff;
  font-size: 13px;
  color: #111827;
  cursor: pointer;
  user-select: none;
  transition:
    background-color .15s ease,
    color .15s ease,
    box-shadow .15s ease,
    transform .1s ease;
}

.chip:hover {
  background: #F3F4FF;
  box-shadow: 0 2px 6px rgba(15, 23, 42, .08);
  transform: translateY(-1px);
}

/* ===== Responsive ===== */

@media (min-width: 1200px) {
  .chatbot {
    width: clamp(560px, 60vw, 980px);
  }
}

@media (max-width: 992px) {
  :root {
    --cb-min: 460px;
    --cb-fluid: 84vw;
    --cb-max: 760px;
  }
}

@media (max-width: 768px) {
  :root {
    --cb-min: 320px;
    --cb-fluid: 94vw;
    --cb-max: 680px;
  }

  .chatbot__title {
    font-size: 20px;
    margin-bottom: 18px;
  }

  .chatbot__search {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .chatbot {
    padding-top: 32px;
  }

  .chatbot__title {
    font-size: 18px;
    line-height: 1.35;
  }

  .chatbot__input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .chatbot__send {
    width: 32px;
    height: 32px;
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .chatbot__title {
    font-size: 17px;
  }
}
.chatbot__placeholder {
  position: absolute;

  left: 34px;
  right: 70px; 

  top: 28px; 

  font-size: 16px;
  line-height: 22px;
  color: #A3A9B3;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  pointer-events: none;
  text-align: left;
  opacity: 1;
  transform: translateY(0);
  transition: opacity .35s ease, transform .35s ease;
}

.chatbot__placeholder.is-fading-out {
  opacity: 0;
  transform: translateY(-4px);
}

:host {
  display: block;
  width: 100%;
  background: transparent; 
}

.chatbot {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: clamp(20px, 5vh, 60px) 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.chatbot__hero {
  display: flex;
  flex-direction: column;
  // align-items: flex-start; 
  align-items: center;
  text-align: left;
  // width: 100%;
  width: -moz-fit-content;
  width: fit-content;
  // max-width: 850px; 
  max-width: 100%;
  margin: 0 auto; 
}

/* ========== Animation Definitions ========== */
@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.anim-1 {
  animation: fadeUp 0.6s ease-out forwards;
  opacity: 0; /* Ẩn ban đầu */
}

.anim-2 {
  animation: fadeUp 0.6s ease-out 0.2s forwards;
  opacity: 0;
}

.anim-3 {
  animation: fadeUp 0.6s ease-out 0.4s forwards;
  opacity: 0;
}


.chatbot__intro {
  display: flex;
  // align-items: flex-start;
  white-space: nowrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 6px;
  // margin-bottom: 2px;
  // margin-left: 50px;
}

// .chatbot__sparkles {
//   padding-top: 4px;
//   flex-shrink: 0;
// }

.chatbot__welcome-text {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.text-gray {
  // font-size: 28px;
  font-size: clamp(18px, 2vw, 24px);
  font-weight: 500;
  color: #64748B;
}

.text-blue {
  // font-size: 28px;
  font-size: clamp(18px, 2vw, 24px); 
  font-weight: 700;
  color: #007ACC;
  line-height: 1.4;
    background: linear-gradient(
    120deg, 
    #007ACC 48%, 
    #ffffff 50%,
    #007ACC 52%
  );

  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  animation: shine-loop 2s linear infinite;
}
.chatbot__sparkles {
  padding-top: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.chatbot__sparkles img {
  display: block;
  animation: star-fancy-loop 3s ease-in-out infinite;
  
  transition: transform 0.4s ease-out; 
  transform-origin: center center;
}

/* KHI HOVER: Dừng hiệu ứng ngay lập tức tại vị trí hiện tại */
.chatbot__sparkles:hover img {
  animation-play-state: paused;
  
  filter: brightness(1.2);
}
@keyframes shine-loop {
  0% {
    background-position: 100% center;
  }
  100% {
    background-position: -100% center;
  }
}
@keyframes cb-title-wave {
  0%   { background-position: 0% 50%; }
  50%  { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
/* Animation kết hợp: Nảy, Phóng to và Xoay */
@keyframes star-fancy-loop {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  50% {
    transform: translateY(-12px) scale(1.2) rotate(180deg);
  }
  100% {
    transform: translateY(0) scale(1) rotate(360deg);
  }
}

@keyframes cb-title-wave {
  0%   { background-position: 0% 50%; }
  50%  { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 2. Cập nhật class Title */
.chatbot__title {
  font-size: clamp(20px, 3vw, 32px);
  display: inline-block;
  text-align: left;
  // align-self: flex-start;
  // margin-left: 50px;
  align-self: center;
  margin-bottom: 24px;
  margin-top: 24px;

  /* --- Font chữ --- */
  font-weight: 700;
  // font-size: clamp(22px, 2.4vw, 24px);

  /* --- Gradient Text --- */
  background: linear-gradient(
    90deg,
    #2384C8 0%,
    #D1D0D5 50%,
    #F8D018 100%
  );
  background-size: 300% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  
  opacity: 0;
  animation: 
    fadeUp 0.6s ease-out 0.2s forwards, 
    cb-title-wave 10s ease-in-out infinite;
}
/* ========== Ô Search ========== */
.chatbot__search {
  position: relative;
  width: 100%;
  background: #ffffff;
  margin: 0 auto 26px;
  border-radius: 24px;
  padding: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.03);
}
// .chatbot__search {
//   width: 900px;
//   max-width: 92%;
//   margin: 0 auto 26px;
//   padding: 12px;
// }

.chatbot__search::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 24px; 
  padding: 2px; 
  
  background: linear-gradient(90deg, #2D8DFE, #9D49F7);
  
  -webkit-mask: 
     linear-gradient(#fff 0 0) content-box, 
     linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.chatbot__input {
  width: 100%;
  height: 80px;
  // padding: 18px 60px 18px 24px;
  padding: 8px 60px 8px 24px;
  border: none;
  background: transparent;
  border-radius: 24px;
  font-size: 16px;
  color: #333;
  resize: none;
  outline: none;
}

.chatbot__input::placeholder {
  color: #A3A9B3;
  font-weight: 400;
}
.chatbot__placeholder {
  position: absolute;

  top: 20px; 
  left: 36px;
  right: 60px;
    font-size: 16px;
  line-height: 1.5;

  color: #A3A9B3;
  pointer-events: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.35s ease, transform 0.35s ease;
    display: flex;
  align-items: center; 
}

.chatbot__placeholder.is-fading-out {
  opacity: 0;
  transform: translateY(-5px);
}

/* ========== Nút Gửi ========== */
.chatbot__send {
  position: absolute;
  right: 15px;
  bottom: 15px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #F3F4F6;
  color: #9CA3AF;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chatbot__send:hover:not(:disabled) {
  background: #E5E7EB;
  color: #6B7280;
}
.chatbot__send:disabled {
  opacity: 0.6;
  cursor: default;
}

/* ========== Chips ========== */
.chatbot__chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center; 
  width: 100%;
}

.chip {
  padding: 8px 18px;
  border-radius: 50px;
  border: 1px solid rgba(229, 231, 235, 0.8);
  background: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  color: #4B5563;
  cursor: pointer;
  transition: 0.2s;
}

.chip:hover {
  background: #ffffff;
  border-color: #D1D5DB;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* ========== Responsive ========== */
@media (max-width: 768px) {
  .chatbot__hero {
    align-items: center;
    text-align: center;
  }
  
  .chatbot__intro {
    flex-direction: column;
    align-items: center;
    white-space: normal;
    margin-left: 0;
  }  
  
  .chatbot__title {
    font-size: 24px;
    margin-left: 0;
  }
  
  .chatbot__input {
    height: 60px;
    padding: 12px 50px 12px 20px;
  }
}