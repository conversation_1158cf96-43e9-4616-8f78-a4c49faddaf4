:host {
  display: block;
//   background-color: #ffffff;
  padding: 60px 0;
  font-family: 'Segoe UI', Robot<PERSON>, sans-serif;
}

.feature-section {
  width: 94vw;
  max-width: 1200px;
  margin: 0 auto;
}

/* --- Header --- */
.feature-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
  }

  .section-desc {
    font-size: 16px;
    line-height: 1.6;
    color: #64748b;
    max-width: 800px;
    margin: 0 auto;
  }
}

/* --- Row Layout --- */
.feature-row {
  display: flex;
  align-items: center;
  gap: 60px;
  margin-bottom: 80px;

  &:last-child {
    margin-bottom: 0;
  }

  /* <PERSON><PERSON><PERSON> ngược thứ tự cho Row 2 */
  &.reverse {
    flex-direction: row-reverse;
  }
}

/* --- Columns --- */
.col-image {
  flex: 1.2; /* Ảnh chiếm phần rộng hơn chút (kho<PERSON>ng 55%) */
  width: 100%;

  .img-wrapper {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.1); /* Đổ bóng nhẹ cho ảnh nổi lên */
    border: 1px solid #f1f5f9;
    
    img {
      width: 100%;
      height: auto;
      display: block;
    }
  }
}

.col-content {
  flex: 1; /* Nội dung chiếm phần còn lại (khoảng 45%) */
  display: flex;
  flex-direction: column;
  gap: 40px; /* Khoảng cách giữa các item 1, 2, 3 */
  padding-top: 20px;
}

/* --- Feature Item (Số + Text) --- */
.feature-item {
  display: flex;
  gap: 20px;

  .item-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e0f2fe; /* Nền xanh nhạt */
    color: #0ea5e9;            /* Chữ xanh đậm */
    font-size: 18px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0; /* Không cho số bị bóp méo */
  }

  .item-info {
    .item-title {
      font-size: 18px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 8px;
    }

    .item-desc {
      font-size: 15px;
      line-height: 1.6;
      color: #64748b;
    }
  }
}

/* --- Responsive Mobile --- */
@media (max-width: 992px) {
  .feature-row {
    flex-direction: column; /* Mobile xếp dọc */
    gap: 40px;
    
    &.reverse {
      flex-direction: column; /* Mobile vẫn xếp ảnh trên, chữ dưới */
    }
  }

  .col-image, .col-content {
    flex: unset;
    width: 100%;
    padding-top: 0;
  }
  
  /* Nếu muốn trên mobile ảnh luôn nằm trên chữ */
  .feature-row.reverse {
    flex-direction: column-reverse; 
  }
}

.col-image, 
.img-wrapper {
  background-color: transparent !important;
  box-shadow: none;
  border: none;
}

.feature-row .col-image,
.feature-row .img-wrapper {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
  outline: none !important;
}

.col-image .img-wrapper img {
  width: 100%;
  height: auto;
  display: block;
}
