import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { environment } from "environments/environment";
import { BehaviorSubject, Observable } from "rxjs";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";

@Injectable({
  providedIn: 'root'
})
export class SharedConversationService {

  constructor(
    private http: HttpClient
  ) {}

  getTitle(): string {
    return 'Shared Conversation';
  }

  getsharedConversationData(shareId: string): Observable<any> {
    return this.http.get<any>(
      `${environment.apiUrl}/chatbot/shares/${shareId}/retrieve_share/`
    );
  }

}
