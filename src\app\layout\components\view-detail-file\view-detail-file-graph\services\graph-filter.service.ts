import { Injectable } from '@angular/core';
import {
  GraphFormState,
  ApiNode,
  GraphNode,
  GraphLink,
  DateFilterMode,
} from '../types/graph.types';
import { GraphCacheService } from './graph-cache.service';
import { GraphApiResponse } from '../types/graph.types';

export interface NodeFilterContext {
  hasAuthorityFilter: boolean;
  normalizedSelectedAuthorities: string[];
  hasTypeFilter: boolean;
  normalizedSelectedTypes: string[];
  hasStatusFilter: boolean;
  normalizedSelectedStatuses: string[];
  hasDateFilter: boolean;
  dateFilterMode: DateFilterMode | null | undefined;
  dateFilterFrom: number | null | undefined;
  dateFilterTo: number | null | undefined;
  hasRelTypeFilter: boolean;
  selectedRelTypes: string[];
  nodesReachableViaRelType: Set<string> | null;
  seedNodeIds: Set<string>; // All seed node IDs
  // Nodes that pass both attribute filters AND relationship filter (AND logic)
  nodesPassingBothFilters: Set<string> | null;
  // Seed nodes that connect to nodes passing both filters
  seedNodesConnectedToFilteredNodes: Set<string> | null;
}

export interface NodeVisualState {
  isBlurred: boolean;
  opacity: number;
}

/**
 * Service for filtering graph nodes and links
 */
@Injectable({
  providedIn: 'root',
})
export class GraphFilterService {
  constructor(private cacheService: GraphCacheService) {}

  /**
   * Build filter context from form state
   */
  buildNodeFilterContext(
    formState: GraphFormState | null,
    seedNodeIds: Set<string>,
    links: GraphLink[],
    nodes?: GraphNode[],
    apiNodeMap?: Map<string, ApiNode>,
    findApiNodeById?: (id: string) => ApiNode | null,
    availableOptions?: {
      authorities?: string[];
      types?: string[];
      statuses?: string[];
      relTypes?: string[];
    }
  ): NodeFilterContext {
    const selectedAuthorities = formState?.selectedCoQuanBanHanh || [];
    const normalizedSelectedAuthorities = selectedAuthorities.map((a) =>
      (a || '').trim()
    );

    const selectedTypes = formState?.selectedBoLocLoaiVanBan || [];
    const normalizedSelectedTypes = selectedTypes.map((t) => (t || '').trim());

    const selectedStatuses = formState?.selectedTinhTrangHieuLuc || [];
    const normalizedSelectedStatuses = selectedStatuses.map((s) =>
      (s || '').trim()
    );

    const selectedRelTypes = formState?.selectedBoLocMoiQuanHe || [];

    const dateFilterMode = formState?.dateFilterMode;
    const dateFilterFrom = formState?.dateFilterFrom;
    const dateFilterTo = formState?.dateFilterTo;

    // Filter is active only if some options are unchecked (array length < total available)
    // If all options are checked, filter is inactive (show everything)
    const hasAuthorityFilter = formState?.selectedCoQuanBanHanh !== undefined && 
      availableOptions?.authorities && 
      normalizedSelectedAuthorities.length < availableOptions.authorities.length;
    
    const hasTypeFilter = formState?.selectedBoLocLoaiVanBan !== undefined && 
      availableOptions?.types && 
      normalizedSelectedTypes.length < availableOptions.types.length;
    
    const hasStatusFilter = formState?.selectedTinhTrangHieuLuc !== undefined && 
      availableOptions?.statuses && 
      normalizedSelectedStatuses.length < availableOptions.statuses.length;
    
    const hasRelTypeFilter = formState?.selectedBoLocMoiQuanHe !== undefined && 
      availableOptions?.relTypes && 
      selectedRelTypes.length < availableOptions.relTypes.length;
    const hasDateFilter =
      dateFilterMode !== null &&
      dateFilterMode !== undefined &&
      dateFilterFrom !== null &&
      dateFilterTo !== null;

    // Relationship filter no longer affects nodes - only links are filtered
    // Keep these for interface compatibility but they won't be used
    const nodesReachableViaRelType: Set<string> | null = null;
    const nodesPassingBothFilters: Set<string> | null = null;
    const seedNodesConnectedToFilteredNodes: Set<string> | null = null;

    return {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
      hasRelTypeFilter,
      selectedRelTypes,
      nodesReachableViaRelType,
      seedNodeIds,
      nodesPassingBothFilters,
      seedNodesConnectedToFilteredNodes,
    };
  }

  /**
   * Get visual state for a node based on filters
   * Note: Relationship filter does NOT affect nodes - only links are filtered by relationship type
   */
  getNodeVisualState(
    nodeId: string,
    apiNode: ApiNode | null,
    context: NodeFilterContext,
    rootNodeId: string,
    findApiNodeById: (id: string) => ApiNode | null
  ): NodeVisualState {
    const {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
    } = context;

    const hasAnyAttributeFilter = hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;

    // Only attribute filters affect nodes - relationship filter does NOT blur nodes
    if (hasAnyAttributeFilter) {
      const nodeData = apiNode ?? findApiNodeById(nodeId);
      let isBlurred = false;
      let nodeOpacity = 1;

      // Check each attribute filter
      if (hasAuthorityFilter) {
        const coQuanBanHanh = (nodeData?.thuoc_tinh?.co_quan_ban_hanh || '').trim();
        if (!this.isChecked(coQuanBanHanh, normalizedSelectedAuthorities)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      if (hasTypeFilter && !isBlurred) {
        const loaiVanBan = (nodeData?.thuoc_tinh?.loai_van_ban || '').trim();
        if (!this.isChecked(loaiVanBan, normalizedSelectedTypes)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      if (hasStatusFilter && !isBlurred) {
        const tinhTrangHieuLuc = (nodeData?.thuoc_tinh?.tinh_trang_hieu_luc || '').trim();
        if (!this.isChecked(tinhTrangHieuLuc, normalizedSelectedStatuses)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      if (hasDateFilter && !isBlurred && dateFilterFrom !== null && dateFilterTo !== null) {
        const dateString = dateFilterMode === 'ban_hanh'
          ? nodeData?.thuoc_tinh?.ngay_ban_hanh || ''
          : nodeData?.thuoc_tinh?.ngay_co_hieu_luc || '';
        if (!this.isDateInRange(dateString, dateFilterFrom, dateFilterTo)) {
          isBlurred = true;
          nodeOpacity = 0.1;
        }
      }

      return { isBlurred, opacity: nodeOpacity };
    }

    // No attribute filters active - highlight all nodes (relationship filter doesn't affect nodes)
    return { isBlurred: false, opacity: 1 };
  }

  /**
   * Build set of active node IDs that pass all filters
   */
  buildActiveNodeIdsForFilters(
    nodes: GraphNode[],
    apiNodeMap: Map<string, ApiNode>,
    context: NodeFilterContext,
    rootNodeId: string,
    findApiNodeById: (id: string) => ApiNode | null
  ): Set<string> {
    const {
      hasAuthorityFilter,
      normalizedSelectedAuthorities,
      hasTypeFilter,
      normalizedSelectedTypes,
      hasStatusFilter,
      normalizedSelectedStatuses,
      hasDateFilter,
      dateFilterMode,
      dateFilterFrom,
      dateFilterTo,
    } = context;

    const activeNodeIds = new Set<string>();

    const requiresFiltering =
      hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;

    if (requiresFiltering) {
      nodes.forEach((node) => {
        const apiNode = apiNodeMap.get(node.id) ?? findApiNodeById(node.id);
        let nodeMatches = true;

        if (!apiNode) {
          nodeMatches = false;
        } else {
          if (hasAuthorityFilter) {
            const coQuanBanHanh =
              (apiNode.thuoc_tinh?.co_quan_ban_hanh || '').trim();
            if (
              !this.isChecked(
                coQuanBanHanh,
                normalizedSelectedAuthorities
              )
            ) {
              nodeMatches = false;
            }
          }

          if (hasTypeFilter && nodeMatches) {
            const loaiVanBan = (apiNode.thuoc_tinh?.loai_van_ban || '').trim();
            if (
              !this.isChecked(loaiVanBan, normalizedSelectedTypes)
            ) {
              nodeMatches = false;
            }
          }

          if (hasStatusFilter && nodeMatches) {
            const tinhTrangHieuLuc = (
              apiNode.thuoc_tinh?.tinh_trang_hieu_luc || ''
            ).trim();
            if (
              !this.isChecked(
                tinhTrangHieuLuc,
                normalizedSelectedStatuses
              )
            ) {
              nodeMatches = false;
            }
          }

          if (
            hasDateFilter &&
            nodeMatches &&
            dateFilterFrom !== null &&
            dateFilterTo !== null
          ) {
            const dateString =
              dateFilterMode === 'ban_hanh'
                ? apiNode.thuoc_tinh?.ngay_ban_hanh || ''
                : apiNode.thuoc_tinh?.ngay_co_hieu_luc || '';
            if (!this.isDateInRange(dateString, dateFilterFrom, dateFilterTo)) {
              nodeMatches = false;
            }
          }
        }

        if (nodeMatches) {
          activeNodeIds.add(node.id);
        }
      });
    }

    // Always include seed nodes
    context.seedNodeIds.forEach((seedId) => activeNodeIds.add(seedId));

    return activeNodeIds;
  }

  /**
   * Determine if a link should be highlighted based on filters
   */
  shouldHighlightLink(
    sourceId: string,
    targetId: string,
    relationshipType: string | undefined,
    context: NodeFilterContext,
    activeNodeIds: Set<string>,
    apiNodeMap: Map<string, ApiNode>,
    findApiNodeById: (id: string) => ApiNode | null,
    rootNodeId: string
  ): boolean {
    const {
      hasRelTypeFilter,
      selectedRelTypes,
      hasAuthorityFilter,
      hasTypeFilter,
      hasStatusFilter,
      hasDateFilter,
    } = context;

    const hasAnyAttributeFilter = hasAuthorityFilter || hasTypeFilter || hasStatusFilter || hasDateFilter;

    // Get visual state for both nodes to check if they're blurred by attribute filters
    const sourceApiNode = apiNodeMap.get(sourceId) ?? findApiNodeById(sourceId);
    const targetApiNode = apiNodeMap.get(targetId) ?? findApiNodeById(targetId);
    
    const sourceVisualState = this.getNodeVisualState(
      sourceId,
      sourceApiNode,
      context,
      rootNodeId,
      findApiNodeById
    );
    const targetVisualState = this.getNodeVisualState(
      targetId,
      targetApiNode,
      context,
      rootNodeId,
      findApiNodeById
    );

    // If either node is blurred by attribute filters, blur the link
    if (sourceVisualState.isBlurred || targetVisualState.isBlurred) {
      return false;
    }

    // Check relationship filter: if relationship filter is active, only show links with checked relationship types
    if (hasRelTypeFilter) {
      // Link must have a checked (allowed) relationship type
      if (!relationshipType || !selectedRelTypes.includes(relationshipType)) {
        return false;
      }
    }

    // Both nodes are visible and link type is allowed (if relationship filter is active)
    return true;
  }

  /**
   * Get all nodes connected via selected relationship types (global, not restricted to seed nodes)
   * Returns set of all node IDs that are part of any link with a selected relationship type
   * Bidirectional: includes both source and target nodes of matching links
   */
  private getNodesConnectedViaRelationshipTypes(
    selectedRelTypes: string[],
    links: GraphLink[]
  ): Set<string> {
    const connectedNodes = new Set<string>();
    
    if (selectedRelTypes.length === 0) {
      return connectedNodes;
    }

    // Find all links with selected relationship types and add both source and target nodes
    links.forEach((link) => {
      const relType = link.__relationshipType;
      if (relType && selectedRelTypes.includes(relType)) {
        const sourceId = String(link.source);
        const targetId = String(link.target);
        connectedNodes.add(sourceId);
        connectedNodes.add(targetId);
      }
    });

    return connectedNodes;
  }

  /**
   * Check if a value is checked (not excluded)
   * Returns true if value is in the checked array, false if excluded
   * Empty/missing values always pass (don't exclude nodes without the attribute)
   * Empty array means all unchecked (exclude everything)
   */
  private isChecked(
    value: string,
    checkedValues: string[]
  ): boolean {
    const normalizedValue = (value || '').trim();
    
    // Empty/missing values always pass - don't exclude nodes without this attribute
    if (!normalizedValue) {
      return true;
    }
    
    if (!checkedValues.length) {
      return false; // All unchecked = exclude everything
    }
    
    return checkedValues.some((checked) => checked === normalizedValue);
  }

  /**
   * Check if a date string falls within the specified year range
   */
  isDateInRange(dateString: string, fromTimestamp: number, toTimestamp: number): boolean {
    if (!dateString) return false;

    try {
      const date = new Date(dateString);
      const time = date.getTime();
      if (isNaN(time)) return false;

      return time >= fromTimestamp && time <= toTimestamp;
    } catch {
      return false;
    }
  }
}

