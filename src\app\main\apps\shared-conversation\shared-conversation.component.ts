import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CoreConfigService } from "@core/services/config.service";

@Component({
  selector: 'app-shared-conversation',
  templateUrl: './shared-conversation.component.html',
  styleUrls: ['./shared-conversation.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SharedConversationComponent implements OnInit {

  shareId!: string;
  isContentScrolled = false;

  constructor(
    private route: ActivatedRoute,
    private _coreConfigService: CoreConfigService
  ) {}

  ngOnInit(): void {
    // Lấy id từ route parameter 'scid'
    this.shareId = this.route.snapshot.paramMap.get('scid')!;

    // Cấu hình layout ẩn menu, navbar và footer
    this._coreConfigService.config = {
      layout: {
        menu: { hidden: true, collapsed: true },
        navbar: { hidden: true },
        footer: { hidden: true },
      }
    };
  }

  onContentScroll(container: HTMLElement): void {
    this.isContentScrolled = container.scrollTop > 0;
  }

  ngOnDestroy(): void {

  }

}
