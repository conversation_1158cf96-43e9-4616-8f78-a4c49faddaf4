import { Component, EventEmitter, Input, Output, ViewEncapsulation, HostListener, OnChanges, SimpleChanges } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-chatbot-detail-file',
  templateUrl: './chatbot-detail-file.component.html',
  styleUrls: ['./chatbot-detail-file.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ChatbotDetailFileComponent implements OnChanges {
  @Input() file: any;
  @Input() showHeader: boolean = true;
  @Output() close = new EventEmitter<void>();
  @Output() openLinkedFile = new EventEmitter<any>(); 
  @Output() save = new EventEmitter<void>(); 
  public safeHtmlContent: SafeHtml = '';

  constructor(private sanitizer: DomSanitizer) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['file']) {
      let content = this.file?.toan_van || '';

      // 1. X<PERSON>a các sự kiện click rác
      content = content.replace(/onclick="[^"]*"/g, "");

      // 2. CHUẨN HÓA LINK
      content = content.replace(/href=["'](legal:[^"']+|local:[^"']+|user:[^"']+|upload:[^"']+)["']/g, (match, url) => {
          return `href="${url}" class="legal-link" onclick="return false"`; 
      });

      // 3. Bypass security
      this.safeHtmlContent = this.sanitizer.bypassSecurityTrustHtml(content);
    }
  }

  onSaveClick() {
    if (this.save) {
        this.save.emit();
    }
  }

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    
    // 1. Tìm thẻ <a> gần nhất
    const anchor = target.tagName === 'A' ? target : target.closest('a');

    if (!anchor) return;

    // 2. Lấy href thực tế
    let href = anchor.getAttribute('href');
    const docIdAttr = anchor.getAttribute('data-id'); 

    if (!href && !docIdAttr) return;

    // 3. Xử lý prefix 'unsafe:' của Angular
    if (href && href.startsWith('unsafe:')) {
      href = href.replace('unsafe:', '');
    }

    // 4. KIỂM TRA LINK ĐẶC BIỆT
    const isSpecialLink = docIdAttr || 
                          (href && (
                            href.startsWith('legal:') || 
                            href.startsWith('user:') || 
                            href.startsWith('upload:') || 
                            href.startsWith('local:')
                          ));

    if (isSpecialLink) {
        event.preventDefault(); 
        event.stopPropagation();
    } else {
        return; 
    }

    // --- LOGIC TRÍCH XUẤT ID VÀ TYPE ---
    let idToOpen = '';
    let typeToOpen = 'searching'; // Mặc định là searching (legal)

    // Case A: data-id
    if (docIdAttr) {
        idToOpen = docIdAttr;
    }
    // Case B: Legal 
    else if (href && href.startsWith('legal:')) {
        const raw = href.replace('legal:', '');
        const parts = raw.split('#');
        idToOpen = parts.length > 1 ? parts[1] : parts[0];
        typeToOpen = 'searching';
    }
    // Case C: User/Upload/Local
    else if (href && (href.startsWith('user:') || href.startsWith('upload:') || href.startsWith('local:'))) {
        const raw = href.replace(/^(user|upload|local):/, '');
        idToOpen = raw.split('#')[0];
        typeToOpen = 'upload'; // Nếu là user/upload thì type thường là upload
    }

    // Lấy text hiển thị trên thẻ a để làm tên file
    const fileName = anchor.textContent?.trim() || anchor.innerText?.trim() || 'Văn bản liên quan';

    if (idToOpen) {
        // Emit một object thay vì chỉ string ID
        this.openLinkedFile.emit({
            id: idToOpen,
            name: fileName, // Truyền tên file sang
            type: typeToOpen
        });
    }
  }
}