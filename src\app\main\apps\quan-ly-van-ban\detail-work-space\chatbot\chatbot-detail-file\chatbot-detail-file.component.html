<div class="chatbot-file-viewer d-flex flex-column h-100 bg-white">
  
<div class="document-header prevent-select" *ngIf="showHeader">
  <div class="d-flex align-items-center flex-grow-1 mr-2" style="min-width: 0; overflow: hidden;">
      
      <div class="header-icon mr-2 flex-shrink-0">
          <i data-feather="file-text" class="width-18px height-18px"></i>
      </div>

      <span class="font-weight-bold text-truncate document-title w-100 d-block" 
            [title]="file?.trich_yeu || file?.name">
          {{ file?.trich_yeu || file?.name || 'Văn bản chi tiết' }}
      </span>
  </div>

  <div class="header-actions flex-shrink-0 ml-1 d-flex align-items-center">
    <button
      *ngIf="!file?.workspace_id" 
      class="view-detail-file-save-button btn round btn-outline-secondary btn-sm font-sm mr-1"
      rippleEffect
      (click)="onSaveClick()"
    >
      <span>Lưu</span>
    </button>
    <button class="btn btn-action-close" (click)="close.emit()" ngbTooltip="Đóng">
        <i data-feather="x" class="width-18px"></i>
    </button>
  </div>
</div>
  <div class="viewer-content flex-grow-1 p-4 overflow-auto">
    <div *ngIf="file?.toan_van" [innerHTML]="safeHtmlContent" class="document-body"></div>
    
    <div *ngIf="!file?.toan_van" class="text-center text-muted mt-5">
      <div class="spinner-border text-primary mb-2" role="status"></div>
      <p class="mt-2">Đang khởi tạo nội dung văn bản...</p>
    </div>
  </div>
</div>