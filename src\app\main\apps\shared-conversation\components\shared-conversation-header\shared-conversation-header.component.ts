import { Component, Input, OnInit, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { SharedConversationService } from '../../shared-conversation.service';
import {
  getLogoImage as getLogoImageHelper,
  getAppN<PERSON> as getAppNameHelper,
  isVP<PERSON><PERSON><PERSON>in,
  VPQH_COLOR_CODE,
} from "app/shared/image.helper";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";

@Component({
  selector: 'app-shared-conversation-header',
  templateUrl: './shared-conversation-header.component.html',
  styleUrls: ['./shared-conversation-header.component.scss']
})
export class SharedConversationHeaderComponent implements OnInit {
	@Input() scrolled = false;
	logoPath = "assets/images/logo/COpenAIlogo.svg";
	appName = "CLS";
	isScrolled = false;
  currentUser: any = null;
  // userName: string = null;
  // role: string = null;
	// isADUser = false;

  constructor(
		private _sharedConversationService: SharedConversationService,
    private _authenService: AuthenticationService
	) {

  }

  ngOnInit(): void {
		this.appName = getAppNameHelper();
		this.logoPath = getLogoImageHelper();

    this._authenService.currentUser.subscribe((res) => {
      this.currentUser = res;
      // this.userName = res?.fullname;
      // this.role = res?.role;
      // this.isADUser = localStorage.getItem('isADUser') === 'true';
    });
  }

	toLoginPage(): void {
    // window.location.href = '/pages/authentication/login-v2';
    window.open('/', '_blank');
  }

  toLandingPage(): void {
    window.open('/home', '_blank');
  }

}
