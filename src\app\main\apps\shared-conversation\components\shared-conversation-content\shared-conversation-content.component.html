<section class="content sc-content">
  <div *ngIf="!isFetching">
    <div class="chatbot-main-flex-size chat-main-flex d-flex flex-column flex-grow-1">
      <div
        *ngIf="messages?.length != 0"
        class="chat-content"
        #chatContainer
      >
        <div class="mt-1 p-0 text-dark">
          <div
            *ngFor="let message of messages; let last = last; let i = index"
            class="message-bubble p-0"
          >
            <span
              class="collapse-icon"
              *ngIf="message.thinking && message.thinking != '<think>'"
              id="chatbot"
              [ngClass]="{
                user: message.type == 'user' || message.role == 'user',
                bot: message.type == 'assistant' || message.role == 'assistant'
              }"
            >
              <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
                <ngb-panel id="ngb-panel-1" [open]="true">
                  <ng-template ngbPanelTitle>
                    <span class="lead collapse-title card-title"
                      ><strong class="font-sm"> Ti<PERSON><PERSON> tr<PERSON><PERSON> tư duy </strong>
                    </span>
                  </ng-template>
                  <ng-template ngbPanelContent>
                    <p
                      class="thinking mt-1 wrap-text"
                      [innerHTML]="message.thinking"
                    ></p>
                  </ng-template>
                </ngb-panel>
              </ngb-accordion>
            </span>
            <div
              class="answer mt-1 user wrap-text"
              *ngIf="message.type == 'user' || message.role == 'user'"
            >
              <!-- selection text -->
              <ng-container *ngIf="message.selection_text">
                <div class="bubble-header">
                  <img
                    class="icon"
                    src="assets/images/icons/selection-text.svg"
                    alt="selection_text"
                  />
                  <p class="selection-text" [ngbTooltip]="message.selection_text">
                    {{ message.selection_text }}
                  </p>
                </div>
              </ng-container>
              <!-- selection text -->
  
              <!-- message user -->
              <span
                class="d-flex ml-25"
                [ngClass]="{
                  'ml-25':
                    message.selected_files?.upload_files?.length ||
                    message.selected_files?.save_files?.length
                }"
                [innerHTML]="message.text || message.answer"
              ></span>
              <!-- message user -->
  
              <!-- selection file -->
              <ng-container
                *ngIf="
                  message.selected_files?.upload_files?.length ||
                  message.selected_files?.save_files?.length
                "
              >
                <!-- Hiển thị upload_files -->
                <div class="file-list-wrapper">
                  <button
                    class="collapse-btn p-0"
                    (click)="collapsed = !collapsed"
                  >
                    Tài liệu đính kèm<img
                      [src]="
                        collapsed
                          ? 'assets/images/icons/up.svg'
                          : 'assets/images/icons/down-black.svg'
                      "
                      alt="collapsed"
                    />
                  </button>
  
                  <div class="file-list" [class.collapsed]="collapsed">
                    <ng-container
                      *ngFor="let file of message.selected_files.upload_files"
                    >
                      <div
                        class="file-item mb-50"
                        [ngbTooltip]="file.name"
                        placement="left"
                        container="body"
                      >
                        {{ file.name }}
                      </div>
                    </ng-container>
  
                    <ng-container
                      *ngFor="let file of message.selected_files.save_files"
                    >
                      <div
                        class="file-item mb-50"
                        [ngbTooltip]="file.name"
                        placement="left"
                        container="body"
                      >
                        {{ file.name }}
                      </div>
                    </ng-container>
                  </div>
                </div>
  
                <!-- Hiển thị upload_files -->
              </ng-container>
              <!-- selection file -->
            </div>
            <p class="gradient-text fly-text" *ngIf="last && !checkDoneThinking">
              {{ statusThinking }}
            </p>
  
            <div
              #replyMessage
              *ngIf="message.type == 'assistant' || message.role == 'assistant'"
              class="answer mt-1 bot"
              [innerHTML]="formatContent(message.text || message.answer) | markdown | safe"
              [attr.id]="'msg-' + i"
              #answerContainer
              (click)="onAnswerClick($event)"
            ></div>
            <ng-container *ngIf="(message.type == 'assistant' || message.role == 'assistant')">
              <!-- <span
                class="cursor-pointer"
                ngbTooltip="Chỉnh sửa"
                container="body"
                (click)="editQuestion(i)"
                ><img
                  src="assets/images/icons/pencil.svg"
                  class="chatbot-icon-padding-5 icon-button"
                  alt="pen"
              /></span> -->
              <!-- <span
                class="cursor-pointer p-50 icon-button"
                appcopy
                [copyTarget]="'#msg-' + i"
                ><img
                  src="assets/images/icons/copy.svg"
                  ngbTooltip="Sao chép"
                  container="body"
                  alt="copy"
              /></span>
              <!-- Nút phát khi chưa đọc -->
              <!-- <span
                *ngIf="!isSpeechToText"
                class="cursor-pointer"
                ngbTooltip="Đọc to"
                container="body"
                (click)="textToSpeech(message)"
              >
                <img
                  src="assets/images/icons/volume.svg"
                  class="icon-button"
                  alt="volume"
                />
              </span> -->
  
              <!-- Nút tạm dừng khi đang phát -->
              <!-- <span
                *ngIf="
                  isSpeechToText &&
                  !isPaused &&
                  idMessageSpeechToText == message.id
                "
                class="cursor-pointer"
                ngbTooltip="Tạm dừng"
                container="body"
                (click)="pauseTextToSpeech()"
              >
                <img
                  src="assets/images/icons/pause.svg"
                  class="icon-button"
                  alt="pause"
                />
              </span> -->
  
              <!-- Nút tiếp tục khi đang tạm dừng -->
              <!-- <span
                *ngIf="
                  isSpeechToText &&
                  isPaused &&
                  idMessageSpeechToText == message.id
                "
                class="cursor-pointer"
                ngbTooltip="Tiếp tục"
                container="body"
                (click)="resumeTextToSpeech()"
              >
                <img
                  src="assets/images/icons/play.svg"
                  class="icon-button"
                  alt="continue"
                />
              </span> -->
  
              <!-- Nút dừng hẳn -->
              <!-- <span
                *ngIf="isSpeechToText && idMessageSpeechToText == message.id"
                class="cursor-pointer"
                ngbTooltip="Dừng đọc"
                container="body"
                (click)="endTextToSpeech()"
              >
                <img
                  src="assets/images/icons/stop-spt.svg"
                  class="icon-button"
                  alt="stop"
                />
              </span> -->
  
              <!-- <span
                class="cursor-pointer"
                ngbTooltip="Câu trả lời tốt"
                container="body"
                (click)="
                  feedbackChatbot(
                    message,
                    message.feedback == 'like' ? 'unlike' : 'like'
                  )
                "
                ><img
                  [src]="
                    message.feedback == 'like'
                      ? 'assets/images/icons/liked.svg'
                      : 'assets/images/icons/like.svg'
                  "
                  alt="like"
                  class="icon-button"
              /></span> -->
              <!-- <span
                class="cursor-pointer"
                ngbTooltip="Câu trả lời không tốt"
                container="body"
                (click)="
                  feedbackChatbot(
                    message,
                    message.feedback == 'dislike' ? 'undislike' : 'dislike'
                  )
                "
                ><img
                  [src]="
                    message.feedback == 'dislike'
                      ? 'assets/images/icons/disliked.svg'
                      : 'assets/images/icons/dislike.svg'
                  "
                  alt="dislike"
                  class="icon-button"
              /></span> -->
            </ng-container>
          </div>
          <!-- <div [style.height.px]="chatHeight" *ngIf="!checkDoneAnswer"></div> -->
        </div>
        <!-- <div
          class="selection-popover"
          *ngIf="showPopover"
          #selectionPopover
          [ngStyle]="{
            top: popoverPosition.top + 'px',
            left: popoverPosition.left + 'px'
          }"
          (click)="$event.stopPropagation()"
        >
          <button class="btn-note" (click)="createNoteFromSelection()">
            <i data-feather="file" size="19"></i>Tạo ghi chú nhanh
          </button>
        </div> -->
      </div>
      <span
        id="scrollDownBtn"
        class="scroll-to-bottom"
        *ngIf="showScrollButton && messages?.length > 0"
        (click)="scrollToBottom2()"
        ngbTooltip="Lướt đến mới nhất"
        container="body"
        ><i data-feather="arrow-down" size="24"></i
      ></span>
      <!-- <div
        class="chat-input-container"
        [ngClass]="{ 'centered-input': messages?.length == 0 }"
      >
        <div class="w-100 text-center mb-1">
          <b class="chatbot-empty-title" *ngIf="messages?.length == 0"
            >Tôi có thể giúp gì cho bạn ?</b
          >
        </div>
        <span class="chatbot-quota-banner text-primary-theme font-weight-bolder">
          Còn {{ quota }} lượt truy vấn
        </span>
        <div class="input-container">
          <div class="function-chat d-flex justify-content-start mb-50">
            <div
              class="functions-button"
              *ngIf="selectedFile.length > 0"
              [ngbPopover]="addFilePopover"
              placement="top"
              [autoClose]="'outside'"
              container="body"
            >
              <img src="assets/images/icons/file-search.svg" alt="file" />
  
              <span (click)="clearDocument($event)">
                {{ selectedFile?.length }} Tài liệu<img
                  src="assets/images/icons/x.svg"
                  alt="x"
                />
              </span>
            </div>
            <div
              class="functions-button"
              *ngIf="isHasTextFromVanBan"
              [ngbTooltip]="selection_text"
              container="body"
            >
              <img src="assets/images/icons/text-scan.svg" alt="file" /> Đã chọn
              ({{ selection_text?.length }})
              <span (click)="selection_text = null; isHasTextFromVanBan = false">
                <img src="assets/images/icons/x.svg" alt="x" />
              </span>
            </div>
          </div>
          <div
            [ngbTooltip]="selection_text"
            container="body"
            class="function-chat d-flex justify-content-start"
            *ngIf="selection_text && !isHasTextFromVanBan"
          >
            <div class="icon-line">
              <div class="icons">
                <img
                  src="assets/images/icons/arrow-right-down.svg"
                  alt="arrow-right-down"
                />
              </div>
              <div class="content-text">
                {{ selection_text }}
              </div>
              <div class="icons" (click)="selection_text = null">
                <img src="assets/images/icons/x.svg" alt="x" />
              </div>
            </div>
          </div>
  
          <textarea
            spellcheck="false"
            id="queryChatbot"
            [(ngModel)]="userInput"
            (keydown)="sendMessage($event)"
            (input)="autoResize($event)"
            class="chat-input w-100"
            placeholder="Nhập truy vấn của bạn vào đây"
            rows="1"
          ></textarea>
          <div
            class="tool-chatbot d-flex justify-content-between align-items-center"
          >
            <div class="tool d-flex align-items-center flex-wrap">
              <div
                *ngIf="type != 5"
                class="cursor-pointer mr-50 icon-button"
                [ngbTooltip]="
                  listDocument.length > 0
                    ? 'Thêm tài liệu'
                    : 'Tải lên hoặc lưu văn bản vào danh sách văn bản để thêm vào đây'
                "
                container="body"
                [ngbPopover]="listDocument.length > 0 ? addFilePopover : null"
                placement="top"
                [autoClose]="'outside'"
              >
                <img src="assets/images/icons/plus-2.svg" alt="file" />
              </div>
              <div
                class="cursor-pointer mr-50 icon-button"
                ngbTooltip="Công cụ"
                container="body"
                [ngbPopover]="showToolsPopover"
                [autoClose]="'outside'"
              >
                <img src="assets/images/icons/adjust.svg" alt="file" />
                <span class="tool-label"> Công cụ </span>
              </div>
              <div
                *ngIf="toolSelected?.length > 0"
                class="d-flex cursor-pointer flex-wrap badge-container"
              >
                <ng-container *ngFor="let item of toolSelected; let i = index">
                  <p
                    class="m-0 d-flex align-items-center badge badge-pill badge-light-primary-theme"
                  >
                    <img [src]="item.icon" alt="icon" /> &nbsp; {{ item.label }}
                    <img
                      (click)="removeTool(i)"
                      [src]="'assets/images/icons/x-danger.svg'"
                      alt="x"
                    />
                  </p>
                </ng-container>
              </div>
            </div>
            <div class="chat-button-wrapper">
              <button
                class="chat-send-button"
                (click)="clickSendMessage()"
                *ngIf="checkDoneAnswer"
                [ngClass]="{ 'disabled-button': userInput === '' }"
              >
                <img src="assets/images/icons/send.svg" alt="send " />
              </button>
              <button
                class="chat-cancel-button"
                (click)="cancelRequest()"
                *ngIf="!checkDoneAnswer"
              >
                <img src="assets/images/icons/stop.svg" alt="send " />
              </button>
            </div>
          </div>
        </div>
  
        <div class="w-100 text-center">
          <small class="chatbot-inline-block text-muted my-xxl-50 my-xl-1 m-lg-1">
            Câu trả lời có thể gặp lỗi, vui lòng kiểm tra lại thông tin.
          </small>
        </div>
      </div> -->
    </div>
  
    <app-shared-conversation-footer></app-shared-conversation-footer>
  </div>
</section>
