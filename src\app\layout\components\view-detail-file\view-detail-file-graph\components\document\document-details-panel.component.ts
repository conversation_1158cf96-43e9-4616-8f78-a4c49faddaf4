import { Component, Input, Output, EventEmitter } from '@angular/core';
import { DocumentData } from '../../types/graph.types';

@Component({
  selector: 'app-document-details-panel',
  templateUrl: './document-details-panel.component.html',
  styleUrls: ['./document-details-panel.component.scss'],
})
export class DocumentDetailsPanelComponent {
  @Input() visible: boolean = false;
  @Input() dataFile: DocumentData = {} as DocumentData;
  @Input() isClauseNode: boolean = false;
  @Input() clauseContent: string = '';
  @Input() isLoadingClauseContent: boolean = false;
  @Input() hasClauseContentError: boolean = false;
  @Input() typeDocument: string = 'csdl';
  @Input() isTimKiemMode: boolean = false;

  @Output() close = new EventEmitter<void>();

  onClose(): void {
    this.close.emit();
  }

  getFormattedClauseContent(): string {
    return this.clauseContent || '';
  }
}

