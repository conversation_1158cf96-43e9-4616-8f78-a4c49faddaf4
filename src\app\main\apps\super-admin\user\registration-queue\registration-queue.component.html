<ngx-datatable
	#registrationQueueTable
	[rows]="registrationQueue"
	[rowHeight]="'auto'"
	class="bootstrap core-bootstrap"
	[columnMode]="ColumnMode.force"
	[footerHeight]="50"
	[scrollbarH]="true"
	[limit]="12"
	[count]="registrationQueueTotal"
	[externalPaging]="true"
	[offset]="registrationQueuePage - 1"
	(page)="onPage($event)"
>
	<ngx-datatable-row-detail [rowHeight]="'auto'">
		<ng-template let-row="row" let-expanded="expanded" ngx-datatable-row-detail-template>
			<div class="ml-75 pl-5 pt-75 mb-1">
				<div>
					<span><strong>Quy mô: </strong> {{ row.scale }}</span>
					<span *ngIf="status == RegistrationQueueStatus.REJECTED" class="ml-1">
						<strong>Gói tài khoản: </strong>
						<span *ngIf="row.user_type === 'individual'"><PERSON><PERSON> nhân</span>
						<span *ngIf="row.user_type === 'organization'">Tổ chức</span>
					</span>
					<span *ngIf="status == RegistrationQueueStatus.REJECTED" class="ml-1">
						<strong>Tên tổ chức: </strong> {{ row.organization_name }}
					</span>
					<span class="ml-1"><strong>Lĩnh vực: </strong> {{ row.industry }}</span>
					<span class="ml-1"><strong>Nguồn: </strong> {{ row.source }}</span>
					<span class="ml-1">
						<strong>Độ quen thuộc: </strong>
						<span *ngIf="row.product_familiarity === 'low'">Thấp</span>
						<span *ngIf="row.product_familiarity === 'medium'">Trung bình</span>
						<span *ngIf="row.product_familiarity === 'high'">Cao</span>
					</span>
					<span class="ml-1"><strong>Ghi chú: </strong> {{ row.description }}</span>
				</div>
			</div>
		</ng-template>
	</ngx-datatable-row-detail>
	<ngx-datatable-column
		[width]="50"
		[resizeable]="false"
		[sortable]="false"
		[draggable]="false"
		[canAutoResize]="false"
	>
		<ng-template let-row="row" let-expanded="expanded" ngx-datatable-cell-template>
			<a
				href="javascript:void(0)"
				[class.datatable-icon-right]="!expanded"
				[class.datatable-icon-down]="expanded"
				ngbTooltip="Xêm thêm/Đóng"
				container="body"
				(click)="rowDetailsToggleExpand(row)"
			>
			</a>
		</ng-template>
	</ngx-datatable-column>

	<!-- <ngx-datatable-column name="STT" [width]="50" [sortable]="false">
		<ng-template let-row="row" let-rowIndex="rowIndex" ngx-datatable-cell-template>
			{{ (registrationQueuePage - 1) * 12 + rowIndex + 1 }}
		</ng-template>
	</ngx-datatable-column> -->
	<ngx-datatable-column name="Họ và tên" prop="user_fullname" [sortable]="false">
		<ng-template ngx-datatable-cell-template let-row="row">
			<div class="text-wrap text-break">
				{{ row?.user_fullname }}
			</div>
		</ng-template>
	</ngx-datatable-column>
	<ngx-datatable-column name="Email" prop="user_email" [sortable]="false">
		<ng-template ngx-datatable-cell-template let-row="row">
			<div class="text-wrap text-break">
				{{ row?.user_email }}
			</div>
		</ng-template>
	</ngx-datatable-column>
	<ngx-datatable-column *ngIf="status != RegistrationQueueStatus.REJECTED" name="Gói tài khoản" prop="user_type" [sortable]="false">
		<ng-template ngx-datatable-cell-template let-row="row">
			<div>
				{{ row.user_type === 'individual' ? 'Cá nhân' : row.user_type === 'organization' ? 'Tổ chức' : row.user_type }}
			</div>
		</ng-template>
	</ngx-datatable-column>
	<ngx-datatable-column *ngIf="status != RegistrationQueueStatus.REJECTED" name="Tên tổ chức" prop="organization_name" [sortable]="false"></ngx-datatable-column>
	<ngx-datatable-column 
		*ngIf="status == RegistrationQueueStatus.REJECTED" 
		name="Từ chối lúc" 
		prop="rejected_at" 
		[sortable]="false">
		<ng-template ngx-datatable-cell-template let-row="row">
			<span>{{ formatRelativeTime(row.rejected_at) }}</span>
		</ng-template>
	</ngx-datatable-column>
	<ngx-datatable-column *ngIf="status == RegistrationQueueStatus.REJECTED" name="Lý do" prop="reject_reason" [sortable]="false">
		<ng-template ngx-datatable-cell-template let-row="row">
			<div class="text-wrap text-break">
				{{ row.reject_reason }}
			</div>
		</ng-template>
	</ngx-datatable-column>
	<ngx-datatable-column *ngIf="status == RegistrationQueueStatus.TO_REVIEW" name="Hành động" [sortable]="false" [width]="80">
		<ng-template ngx-datatable-cell-template let-row="row">
			<span container="body">
				<a
					href="javascript:void(0);"
					class="hide-arrow color-333"
					id="dropdownBrowserState"
					data-toggle="dropdown"
					aria-haspopup="true"
					aria-expanded="false"
					placement="top"
					ngbTooltip="Phê duyệt"
					container="body"
					(click)="modalOpen(approveModal, row)"
				>
					<i data-feather="check" class="mx-50"></i>
				</a>
				<a
					href="javascript:void(0);"
					class="hide-arrow color-333"
					id="dropdownBrowserState"
					data-toggle="dropdown"
					aria-haspopup="true"
					aria-expanded="false"
					ngbTooltip="Từ chối"
					placement="top"
					container="body"
					(click)="modalOpen(rejectModal, row)"
				>
					<i data-feather="x" class="mx-50"></i>
				</a>
			</span>
		</ng-template>
	</ngx-datatable-column>
</ngx-datatable>


<!-- Approve Modal -->
<ng-template #approveModal let-modal>
	<div class="swal2-modal swal2-show p-2 border-radius-10px d-block">
		<div class="swal2-icon swal2-warning swal2-icon-show d-flex">
			<div class="swal2-icon-content">!</div>
		</div>
		<h2 class="swal2-title">Xác nhận phê duyệt</h2>
		<div class="swal2-content text-left mt-1">
			<div class="swal2-field">
				<dl class="row">
					<dt class="col-sm-3"><b>Họ và tên:</b></dt>
					<dd class="col-sm-9">{{ approveForm.userFullname || '' }}</dd>
				</dl>
				<dl class="row">
					<dt class="col-sm-3"><b>Email:</b></dt>
					<dd class="col-sm-9">{{ approveForm.userEmail || '' }}</dd>
				</dl>
			</div>

			<div class="swal2-field">
				<label class="mb-25"><b>Liên kết với tổ chức:</b>
					<br>
					<small class="text-muted">Chọn tổ chức đã có trên hệ thống để thực hiện liên kết</small>
				</label>
				<input
					class="form-control cursor-pointer bg-transparent"
					[placeholder]="'Chọn tổ chức'"
					[value]="selectedOrganizationName"
					(click)="modalOpen(organizationModal)"
					readonly
				/>
			</div>

			<div class="swal2-field mt-1">
				<span class="text-warning">Thao tác này sẽ kích hoạt tài khoản <b>{{ approveForm.userFullname }}</b>.</span>
				<span class="text-warning">Bạn có chắc chắn muốn phê duyệt?</span>
			</div>
		</div>
		
		<div class="swal2-actions">
			<button type="button" class="btn btn-secondary mr-1" (click)="modal.close('Dismiss click')">
				Hủy
			</button>
			<button type="button"
				class="btn btn-primary"
				(click)="updateRegistration(RegistrationQueueStatus.APPROVED)">
				Phê duyệt
			</button>
		</div>
	</div>
</ng-template>

<!-- Reject Modal -->
<ng-template #rejectModal let-modal>
	<div class="swal2-modal swal2-show p-2 border-radius-10px d-block">
		<div class="swal2-icon swal2-warning swal2-icon-show d-flex">
			<div class="swal2-icon-content">!</div>
		</div>
		<h2 class="swal2-title">Từ chối phê duyệt</h2>
		<div class="swal2-content text-left">
			<div class="swal2-field my-1">
				<b>Vui lòng nhập lý do từ chối phê duyệt tài khoản {{ approveForm.userFullname }}</b>
			</div>

			<div class="swal2-field">
				<div class="custom-control custom-radio d-flex align-items-center my-25">
					<input type="radio" id="customRadio1" name="customRadio" class="custom-control-input"
						[value]="'Tài khoản ảo'" 
						(change)="rejectReason = 'Tài khoản ảo'; isOtherReasonChecked = false" 
						[checked]="rejectReason === 'Tài khoản ảo'" />
					<label class="custom-control-label" for="customRadio1">Tài khoản ảo</label>
				</div>
				<div class="custom-control custom-radio d-flex align-items-center my-25">
					<input type="radio" id="customRadio2" name="customRadio" class="custom-control-input"
						[value]="'Spam'" 
						(change)="rejectReason = 'Spam'; isOtherReasonChecked = false" 
						[checked]="rejectReason === 'Spam'" />
					<label class="custom-control-label" for="customRadio2">Spam</label>
				</div>
				<div class="custom-control custom-radio d-flex align-items-center my-25">
					<input type="radio" id="customRadio3" name="customRadio" class="custom-control-input"
						[value]="'Thông tin không hợp lệ'" 
						(change)="rejectReason = 'Thông tin không hợp lệ'; isOtherReasonChecked = false" 
						[checked]="rejectReason === 'Thông tin không hợp lệ'" />
					<label class="custom-control-label" for="customRadio3">Thông tin không hợp lệ</label>
				</div>
				<div class="custom-control custom-radio d-flex align-items-center my-25">
					<input type="radio" id="customRadio4" name="customRadio" class="custom-control-input"
						[value]="''" 
						(change)="isOtherReasonChecked = true; rejectReason = ''"
						[checked]="isOtherReasonChecked" />
					<label class="custom-control-label" for="customRadio4">Khác</label>
				</div>
				<div *ngIf="isOtherReasonChecked" class="mt-2">
					<textarea
						class="form-control"
						placeholder="Nhập lý do khác"
						[(ngModel)]="rejectReason"
						rows="3"
						required
					></textarea>
					<div *ngIf="isOtherReasonChecked && rejectReason?.trim() === ''" class="text-danger mt-1" style="font-size: 0.92em;">
						Vui lòng nhập lý do từ chối.
					</div>
				</div>
			</div>
		</div>
		
		<div class="swal2-actions">
			<button type="button" class="btn btn-secondary mr-1" (click)="modal.close('Dismiss click')">
				Hủy
			</button>
			<button type="button"
				class="btn btn-primary"
				[disabled]="rejectReason.trim() === ''"
				(click)="updateRegistration(RegistrationQueueStatus.REJECTED)">
				Từ chối
			</button>
		</div>
	</div>
</ng-template>

<!-- Modal chọn tổ chức -->
<ng-template #organizationModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Chọn tổ chức</h4>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
  </div>

  <div class="modal-body">
		<!-- <div class="text-primary fw-semibold ms-auto mb-25">
      Đang chọn: {{ tempSelectedOrganization?.name || 'Chưa chọn' }}
    </div> -->
    <div class="mb-1 position-relative">
      <div class="input-with-icon position-relative">
        <button type="button" class="btn-icon-inside left-icon">
          <i data-feather="search"></i>
        </button>
        <input
          type="text"
          class="form-control"
          placeholder="Tìm kiếm theo tên tổ chức"
          (input)="orgSearchText = $any($event.target).value || ''"
        />
      </div>
    </div>

    <div class="tree-wrap">
      <ng-container *ngFor="let node of treeOrg">
        <ng-container
          *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: node, level: 0, search: orgSearchText }"
        ></ng-container>
      </ng-container>
    </div>

    <ng-template #organizationTreeNodeTpl let-node let-level="level" let-search="search">
      <ng-container *ngIf="shouldShowNode(node, search)">
        <div
					class="tree-row cursor-pointer d-flex align-items-center"
          [ngStyle]="{'padding-left.px': 12 + level * 20}"
          [class.has-children]="node.children?.length"
          [class.active]="isTempSelected(node)"
          (click)="setTempOrganization(node)"
          (dblclick)="confirmOrganization(modal, node)"
        >
					<span class="tree-expand-holder d-flex align-items-center justify-content-center me-1 width-24px">
						<ng-container *ngIf="node.children?.length; else emptyHolder">
							<button
								type="button"
								class="toggle btn p-0 border-0 bg-transparent d-flex align-items-center width-24px"
								(click)="$event.stopPropagation(); node.showChildren = !node.showChildren"
								[attr.aria-label]="node.showChildren ? 'Collapse' : 'Expand'"
								tabindex="-1"
							>
								<i
									class="feather"
									[ngClass]="{
										'icon-chevron-down': node.showChildren,
										'icon-chevron-right': !node.showChildren
									}"
								></i>
							</button>
						</ng-container>
						<ng-template #emptyHolder>
							<span class="width-24px height-24px"></span>
						</ng-template>
					</span>
					<i class="feather node-icon icon-briefcase me-75"></i>
					<div class="node-text flex-grow-1">
            <span class="node-name fw-semibold">
              <strong>{{ node.name }}</strong>
            </span>
          </div>
          <span class="badge badge-light-primary ms-auto" *ngIf="isTempSelected(node)">Đang chọn</span>
        </div>
        <div *ngIf="node.children?.length && node.showChildren">
          <ng-container *ngFor="let child of node.children">
            <ng-container
              *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: child, level: level + 1, search: search }"
            ></ng-container>
          </ng-container>
        </div>
      </ng-container>
    </ng-template>
  </div>

  <div class="modal-footer border-0">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
      Hủy
    </button>
    <button
      type="button"
      class="btn btn-primary-theme"
      [disabled]="!tempSelectedOrganization"
      (click)="confirmOrganization(modal)"
    >
      Chọn
    </button>
  </div>
</ng-template>