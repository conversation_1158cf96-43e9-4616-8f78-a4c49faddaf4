:host {
  display: block;
  height: 100%;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

.clsc-file {
  display: flex;
  height: 100%;
  background: #f4f5f7;
}

.clsc-file,
.clsc-file * {
  font-family: inherit;
}

.clsc-file__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background: transparent;
}

.clsc-file__main::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 120px;
  background: #ffffff;
  border-bottom: 1px solid #edf2f7;
  z-index: -1;
}

.clsc-file__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;

  background: #ffffff;
  margin: 0;
  padding: 16px 24px 8px;
  border-radius: 0;
  border: none;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.clsc-file__header-left {
  flex: 1;
  min-width: 0; 
}

.clsc-file__title {
  font-size: 18px;
  margin: 0 0 4px;
  font-weight: 700;
  color: #0f172a;

  white-space: normal;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-word;
}

.clsc-file__subtitle {
  margin: 0;
  font-size: 14px;
  color: #6b7280;

  .subtitle-animate-pulse {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2563eb;
    animation: clsc-loading-pulse 2s ease-in-out infinite;
    margin-right: 5px;
  }
}

.clsc-file__body {
  flex: 1;
  // margin-top: 12px;
  overflow-y: auto;
  padding: 0 24px;
}

.clsc-file__contents {
  padding: 24px 0 64px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  .highlight-scroll {
    background-color: #fff59d;
    transition: background-color 2s ease;
  }
}
