:host {
  display: block;
//   background-color: #ffffff;
  padding: 60px 0 80px;
  font-family: 'Segoe UI', Roboto, sans-serif;
}

.container {
  width: 94vw;
  max-width: 1200px;
  margin: 0 auto;
}

/* --- Header Styles --- */
.section-header {
  text-align: center;
  margin-bottom: 50px;

.section-title {
    font-family: 'Segoe UI', sans-serif;
    font-size: 32px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
  }

  .section-desc {
    font-size: 16px;
    line-height: 1.6;
    color: #64748b;
    max-width: 800px;
    margin: 0 auto;
  }
}

/* --- Bento Grid Layout --- */
.bento-grid {
  display: grid;
  /* Chia làm 6 cột đều nhau */
  grid-template-columns: repeat(6, 1fr);
  grid-auto-rows: minmax(100px, auto); 
  gap: 16px;
}

/* --- Card Styles Chung --- */
.grid-card {
  border-radius: 8px;
  padding: 20px;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center; /* Mặc định căn gi<PERSON>a dọc */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.15);
    z-index: 1;
  }

  .card-number {
    font-size: clamp(24px, 3vw, 36px);
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 1.2;
  }

  .card-label {
    font-size: 14px;
    font-weight: 600;
    opacity: 0.9;
  }
  
  /* Cho các ô nhỏ chỉ có text */
  .card-label-only {
    font-size: 15px;
    font-weight: 600;
    line-height: 1.4;
  }
  
  /* Wrapper để căn giữa nội dung cho các ô lớn */
  .card-content-wrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }
}

/* --- Màu sắc (Lấy mã màu từ ảnh) --- */
.bg-red { background-color: #c0392b; } /* Hiến pháp */
.bg-orange { background-color: #f39c12; } /* Luật, Bộ luật */
.bg-green { background-color: #27ae60; } /* Nghị quyết */
.bg-blue-dark { background-color: #2980b9; } /* Nghị định, Thông báo */
.bg-blue { background-color: #4285f4; } /* Công văn, Thông báo 17k */
.bg-blue-darker { background-color: #1e3a8a; } /* Kế hoạch, Quyết định */
.bg-yellow { background-color: #f1c40f; } /* Pháp lệnh */
.bg-red-light { background-color: #e74c3c; } /* Khác */


/* --- Cấu hình Vị trí Grid --- */

/* Hàng 1 */
.card-hien-phap { grid-column: 1 / 2; grid-row: 1; }
.card-bo-luat   { grid-column: 2 / 3; grid-row: 1; }
.card-nghi-quet { grid-column: 3 / 5; grid-row: 1; } /* Span 2 cột */
.card-nghi-dinh { grid-column: 5 / 6; grid-row: 1; }
.card-cong-van  { 
  grid-column: 6 / 7; 
  grid-row: 1 / 4; /* Span dọc từ hàng 1 đến hết hàng 3 */
}

/* Hàng 2 */
.card-luat { 
  grid-column: 1 / 3; 
  grid-row: 2 / 4; /* Span 2x2 */
.card-content-wrap {
    flex-direction: row !important;
    align-items: center; 
    justify-content: flex-start !important; 
    gap: 12px;
    
    /* padding-left: 10px; */ 
  }

  .card-number { 
    font-size: 52px; 
    font-weight: 700;
    margin-bottom: 0 !important;
    line-height: 1;
  }
  
  .card-label {
    font-size: 20px;
    font-weight: 600;
    margin-top: 8px;
  }
}
.card-phap-lenh { 
  grid-column: 3 / 4; 
  grid-row: 2 / 4; /* Span dọc */
}
.card-ke-hoach { grid-column: 4 / 6; grid-row: 2; } /* Span ngang */

/* Hàng 3 */
.card-thong-bao-1 { grid-column: 4 / 6; grid-row: 3; } /* Nằm dưới Kế hoạch */

/* Hàng 4 (Dưới cùng) */
.card-quyet-dinh { 
  grid-column: 1 / 5;
  grid-row: 4; 
}
.card-thong-bao-2 { grid-column: 5 / 6; grid-row: 4; }
.card-khac        { grid-column: 6 / 7; grid-row: 4; }

.card-hien-phap, 
.card-bo-luat
{
  flex-direction: row !important;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  
  .card-number {
    margin-bottom: 0 !important;
    font-size: 32px;
    line-height: 1;
    flex-shrink: 0;
  }

  .card-label {
    text-align: left;
    font-size: 15px;
    line-height: 1.2;
  }
}

/* --- Responsive Mobile --- */
@media (max-width: 992px) {
  .bento-grid {
    /* Mobile chuyển về 2 cột */
    grid-template-columns: 1fr 1fr;
    grid-auto-rows: auto;
  }

  .grid-card {
    grid-column: auto !important;
    grid-row: auto !important;
    min-height: 120px;
  }
  
  /* Giữ một số ô quan trọng to hơn trên mobile */
  .card-quyet-dinh, .card-cong-van {
    grid-column: span 2 !important; /* Full width */
  }
}