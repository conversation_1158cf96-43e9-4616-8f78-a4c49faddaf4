 import {
  Component,
  OnInit,
  ViewEncapsulation,
  OnDestroy,
  Input,
  OnChanges,
  Output,
  EventEmitter,
  SimpleChanges,
  ChangeDetectorRef,
  Renderer2, Inject,
  HostListener,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { DragulaService } from 'ng2-dragula';
import { CmsService } from '../cms.service';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidatorFn } from '@angular/forms';
import { NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { Subscription, fromEvent, of } from 'rxjs';
import { formatRelativeTime } from '../../super-admin/email/helpers';
import { ToastrService } from 'ngx-toastr';
import { AuthenticationService } from 'app/auth/service';
import Quill from 'quill';
import { DOCUMENT } from '@angular/common';
import * as feather from 'feather-icons';
import { switchMap, takeUntil, catchError } from 'rxjs/operators';

@Component({
  selector: 'app-list-work',
  templateUrl: './list-work.component.html',
  styleUrls: ['./list-work.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ListWorkComponent implements OnInit, OnDestroy, OnChanges {
  @Input() public cmsData: any;
  @Input() public writerUsers: any;
  @Input() public reviewerUsers: any;
  @Input() public adminUsers: any;

  @Output() createdOrUpdated: EventEmitter<any> = new EventEmitter<any>();
  @Output() reloadCmsUser: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('VideoUrlModal', { static: false }) VideoUrlModal: any;
  @ViewChild('AssignModal', { static: false }) AssignModal: any;
  @ViewChild('modalContent', { static: false }) modalContent: any;
  @ViewChild('thumbInput', { static: false }) thumbInput: any;

  public todayISO: string = this.toISODate(new Date());

  private toISODate(d: Date): string {
    const tz = d.getTimezoneOffset();
    const local = new Date(d.getTime() - tz * 60000);
    return local.toISOString().slice(0, 10);
  }

  public notPastDate: ValidatorFn = (control: AbstractControl) => {
    const v: string = control.value;
    if (!v) return null;
    return v < this.todayISO ? { pastDate: true } : null;
  };

  public tmpVideoUrl = '';
  private quillWaitingUrl: any = null;

  private assignModalType: 'writer' | 'reviewer' = 'writer';
  private assignModalRef: any = null;

  public thumbPreviewUrl: string | null = null;
  public isDragging = false;
  private dragFromColId: string | null = null;
  public allowedTargets = new Set<string>();
  private static readonly DRAG_GROUP = 'multiple-list-group';
  public writerOptions: any[] = [];
  private buildWriterOptions() {
    // base = list writer từ @Input
    const base = Array.isArray(this.writerUsers) ? [...this.writerUsers] : [];

    const me = this._auth?.currentUserValue;
    if (this.isAdmin && me) {
      const myId = me.id;

      // dùng helper đã có để lấy id trong item
      const exists = base.some((u: any) => {
        const entity = u?.user || u?.user_info || u;
        return this.getEntityId(entity) === myId;
      });

      // nếu admin chưa nằm trong list writer thì thêm vào
      if (!exists) {
        base.unshift({
          user: {
            id: myId,
            fullname: me.fullname || me.email || 'Tài khoản hiện tại',
            avatar: me.avatar || null,
          },
        });
      }
    }
    this.writerOptions = base;
  }

  private ensureDragulaGroup(opts: any) {
    const name = ListWorkComponent.DRAG_GROUP;
    const bag = this._dragulaService.find(name);
    if (bag) {
      this._dragulaService.destroy(name);
    }
    this._dragulaService.createGroup(name, opts);
  }

  private destroyDragulaGroupIfAny() {
    const bag = this._dragulaService.find(ListWorkComponent.DRAG_GROUP);
    if (bag) this._dragulaService.destroy(ListWorkComponent.DRAG_GROUP);
  }

  private computeAllowedTargets(fromColId: string) {
    this.allowedTargets.clear();
    const from = ListWorkComponent.STATUS_MAP[fromColId];
    if (!from) return;

    Object.entries(ListWorkComponent.STATUS_MAP).forEach(([colId, to]) => {
      if (!to) return;
      if (this.isAllowedTransition(from, to) && to !== from) {
        this.allowedTargets.add(colId);
      }
    });
  }

  public isColAllowed(colId: string): boolean {
    if (!this.isDragging) return false;
    if (!this.dragFromColId) return false;
    // Luôn cho phép cột nguồn sáng nhẹ để user biết vị trí xuất phát
    if (colId === this.dragFromColId) return true;
    return this.allowedTargets.has(colId);
  }

  private clearDragHints() {
    this.isDragging = false;
    this.dragFromColId = null;
    this.dragFromStatus = null;
    this.hoverColId = null;
    this.allowedTargets.clear();
    this.cdr.markForCheck();
  }
  get isQA(): boolean {
    return this.contentForm?.get('class')?.value === 'qa';
  }
  triggerPickThumb() {
    const input = this.createHiddenFileInput('image/*');

    input.onchange = (ev: any) => {
      const file = (ev.target as HTMLInputElement).files?.[0];
      this.document.body.removeChild(input);

      if (!file) return;
      if (!this.validateImage(file)) return;

      // Lưu vào FormData
      this.contentForm.patchValue({ image: file });
      this.contentForm.get('image')?.markAsTouched();
      this.contentForm.get('image')?.updateValueAndValidity();

      // Preview
      const reader = new FileReader();
      reader.onload = () => (this.thumbPreviewUrl = String(reader.result || ''));
      reader.readAsDataURL(file);
    };

    try { (input as HTMLInputElement).value = ''; } catch {}
    setTimeout(() => input.click(), 0);
  }
  
  onThumbFileSelected(e: Event) {
    const input = e.target as HTMLInputElement;
    const file = input.files?.[0];
    if (!file) return;

    if (!this.validateImage(file)) return;

    this.contentForm.patchValue({ image: file });
    this.contentForm.get('image')?.markAsTouched();
    this.contentForm.get('image')?.updateValueAndValidity();

    const reader = new FileReader();
    reader.onload = () => this.thumbPreviewUrl = String(reader.result || '');
    reader.readAsDataURL(file);
  }

  removeThumb() {
    this.contentForm.patchValue({ image: null });
    this.thumbPreviewUrl = null;
    this.existingImageUrl = null;
    if (this.thumbInput?.nativeElement) this.thumbInput.nativeElement.value = '';
    this.setImageValidators();
  }

  // thêm biến giữ modal ref
  private modalRef: NgbModalRef | null = null;
  private dragSub?: Subscription;
  private dropModelSub?: Subscription;
  private slugWarnedOnce = false;
  private readonly AUTO_SLUG_FROM_TITLE = false;

  private defaultForm() {
    return {
      class: null,
      id: null,
      title: '',
      // Content
      type: 'NORMAL',
      body: '',
      description: '',
      // QA
      question: '',
      answer: '',
      // Common
      image: null,
      slug: '',
      keywords: '',
      publish_date: '',
      topic: '',
      writer: null,
      reviewer: null,
      admin: null
    };
  }

  private resetUIState() {
    this.thumbPreviewUrl = null;
    this.existingImageUrl = null;
    this.tmpVideoUrl = '';
    this.isBodyFS = this.isQuestionFS = this.isAnswerFS = false;
    this.selectedUserId = null;
    this.lockBodyScroll(false);
    if (this.thumbInput?.nativeElement) this.thumbInput.nativeElement.value = '';
  }

  // dropdown state
  private currentVideoMenu?: HTMLElement;
  public contentForm: FormGroup;

  public selectedWorkTypes: any[] = [];
  public selectedWork: any[] = [];
  public selectedUserId: number | null = null;
  public workSearchTerm: string = ''; 

  // 7 Lists phân loại trạng thái (raw, chưa filter)
  private _listDraft: any[] = [];
  private _listWriting: any[] = [];
  private _listReviewing: any[] = [];
  private _listRejected: any[] = [];
  private _listCompleted: any[] = [];
  private _listPublished: any[] = [];
  private _listUnpublished: any[] = [];
  private _listArchived: any[] = [];
  private filterForUI(list: any[]): any[] {
    // lọc theo loại (content/qa) rồi áp phạm vi writer
    const base = this.filterBySelectedTypes(list);
    const scoped = this.applyWriterScope(base);
    // return this.applyWriterScope(base);
    return this.applySearch(scoped); 
  }
  // Filtered lists cho UI binding
  public get listDraft(): any[] {
    return this.filterForUI(this._listDraft);
  }
  public get listWriting(): any[] {
    return this.filterForUI(this._listWriting);
  }
  public get listReviewing(): any[] {
    return this.filterForUI(this._listReviewing);
  }
  public get listRejected(): any[] {
    return this.filterForUI(this._listRejected);
  }
  public get listCompleted(): any[] {
    return this.filterForUI(this._listCompleted);
  }
  public get listPublished(): any[] {
    return this.filterForUI(this._listPublished);
  }
  public get listUnpublished(): any[] {
    return this.filterForUI(this._listUnpublished);
  }
  public get listArchived(): any[] {
    return this.filterForUI(this._listArchived);
  }

  public subs = new Subscription();

  public static readonly STATUS_MAP: { [key: string]: string } = {
    'multiple-list-group-draft': 'DRAFT',
    'multiple-list-group-writing': 'WRITING',
    'multiple-list-group-reviewing': 'REVIEWING',
    'multiple-list-group-completed': 'COMPLETED',
    'multiple-list-group-published': 'PUBLISHED',
    'multiple-list-group-unpublished': 'UNPUBLISHED',
    'multiple-list-group-archived': 'ARCHIVED',
    'multiple-list-group-rejected': 'REJECTED',
  };

  public editorModules = {
    toolbar: [
      ['bold','italic','underline','strike'],
      [{ header: [1,2,3,4,5,6,false] }],
      [{ size: [] }],
      [{ color: [] }, { background: [] }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ align: [] }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ indent: '-1' }, { indent: '+1' }],
      ['blockquote', 'code-block'],
      ['link','image','video','formula'],
      ['clean']
    ],
    syntax: false,
  };

  isBodyFS = false;
  isQuestionFS = false;
  isAnswerFS = false;

  // ====== RBAC: roles của user hiện tại ======
  public myRoles: Array<'WRITER'|'REVIEWER'|'ADMIN'> = [];
  public isWriter = false;
  public isReviewer = false;
  public isAdmin = false;
  public isSuperAdmin = false;
  // ==========================================
  private IMG_MAX_MB = 10;
  private IMG_ACCEPT = ['image/png','image/jpeg','image/webp','image/gif'];
  public dragFromStatus: string | null = null;
  public hoverColId: string | null = null;

  public static readonly STATUS_LABEL: Record<string, string> = {
    DRAFT: 'Mới khởi tạo',
    WRITING: 'Đang viết',
    REVIEWING: 'Chờ phê duyệt',
    REJECTED: 'Từ chối',
    COMPLETED: 'Hoàn thành',
    PUBLISHED: 'Đã công bố',
    UNPUBLISHED: 'Chưa công bố',
    ARCHIVED: 'Lưu trữ',
  };

  statusLabel(code?: string | null){
    return ListWorkComponent.STATUS_LABEL[code || ''] || '';
  }

  isIntentAllowed(toCode: string){
    const from = this.dragFromStatus;
    return !!(from && this.isAllowedTransition(from, toCode));
  }
  private colIdToStatus(colId: string | null): string | null {
    if (!colId) return null;
    return ListWorkComponent.STATUS_MAP[colId] || null;
  }

  getColLabelByColId(colId: string): string {
    const code = this.colIdToStatus(colId);
    return this.statusLabel(code);
  }

  /** Có hiển thị chip ở cột này không?
   *  - Chỉ khi đang kéo
   *  - Không hiển thị ở cột nguồn
   *  - Hiển thị ở mọi cột đích hợp lệ (theo RBAC/isAllowedTransition)
   */
  showChip(colId: string): boolean {
    if (!this.isDragging) return false;
    if (!this.dragFromColId) return false;
    if (colId === this.dragFromColId) return false;

    return this.allowedTargets.has(colId);
  }
  constructor(
    private _dragulaService: DragulaService,
    private _toastSerive: ToastrService,
    private _cmsService: CmsService,
    private _modalService: NgbModal,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private _auth: AuthenticationService,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
  ) {
    this.contentForm = this.fb.group({
      class: [null, Validators.required],
      id: [null],
      title: ['', [Validators.required, Validators.maxLength(this.titleMax)]],

      // For Content
      body: [''],
      description: [''],
      type: ['NORMAL', Validators.required],

      // For QA
      question: [''],
      answer: [''],

      image: [null],
      slug: [''],
      keywords: [''],
      publish_date: [''],
      topic: ['', Validators.required],

      writer: [null, Validators.required],
      reviewer: [null]
    });

    const Parchment = Quill.import('parchment');
    const AlignStyle = new Parchment.Attributor.Style('align', 'text-align', {
      scope: Parchment.Scope.BLOCK,
      whitelist: ['left','right','center','justify'],
    });
    const FontStyle = new Parchment.Attributor.Style('font','font-family');
    const SizeStyle = new Parchment.Attributor.Style('size','font-size', {
      whitelist: ['small','large','huge'],
    });
    const ColorStyle = new Parchment.Attributor.Style('color','color');
    const BackgroundStyle = new Parchment.Attributor.Style('background','background-color');

    Quill.register(AlignStyle, true);
    Quill.register(FontStyle, true);
    Quill.register(SizeStyle, true);
    Quill.register(ColorStyle, true);
    Quill.register(BackgroundStyle, true);
    const BlockEmbed = Quill.import('blots/block/embed');
    class HtmlIframeBlot extends BlockEmbed {
      static blotName = 'html-iframe';
      static tagName = 'iframe';

      static create(value: string) {
        const node: HTMLElement = super.create() as any;
        node.setAttribute('src', value);
        node.setAttribute('frameborder', '0');
        node.setAttribute('allowfullscreen', 'true');
        (node as any).style.width = '100%';
        (node as any).style.height = '400px';
        return node;
      }
      static value(node: HTMLElement) {
        return node.getAttribute('src');
      }
    }
    Quill.register(HtmlIframeBlot);

    class HtmlVideoBlot extends BlockEmbed {
      static blotName = 'html-video';
      static tagName = 'video';

      static create(value: string) {
        const node: HTMLElement = super.create() as any;
        node.setAttribute('controls', 'true');
        node.setAttribute('preload', 'metadata');
        node.setAttribute('src', value);
        (node as any).style.maxWidth = '100%';
        (node as any).style.height = 'auto';
        return node;
      }

      static value(node: HTMLElement) {
        return node.getAttribute('src');
      }
    }
    Quill.register(HtmlVideoBlot);
  }

  public readonly titleMax = 255;
  get titleCount(): number {
    return (this.contentForm?.get('title')?.value || '').length;
  }
  private insertIframeToEditor(quill: any, url: string) {
    const range = quill.getSelection(true) || { index: quill.getLength(), length: 0 };
    quill.insertEmbed(range.index, 'html-iframe', url, 'user');
    quill.setSelection(range.index + 1);
  }

  private normalizeVideoUrlForIframe(raw: string): string | null {
    const url = raw.trim();

    const yt = /(youtube\.com\/watch\?v=|youtu\.be\/)([A-Za-z0-9_-]{6,})/i.exec(url);
    if (yt) return `https://www.youtube.com/embed/${yt[2]}`;

    const vm = /vimeo\.com\/(\d+)/i.exec(url);
    if (vm) return `https://player.vimeo.com/video/${vm[1]}`;
    return null; 
  }

  private getMyUserId(): number | null {
    const me = this._auth?.currentUserValue;
    return me?.id ?? null;
  }

  private getEntityId(entity: any): number | null {
    if (entity == null) return null;
    if (typeof entity === 'number') return entity;
    if (typeof entity === 'object') {
      return entity.id ?? entity.user?.id ?? entity.user_info?.id ?? entity.user_id ?? null;
    }
    return null;
  }

  private applyWriterScope(list: any[]): any[] {
    if (this.isAdmin || this.isReviewer) return list;

    if (this.isWriter) {
      const myId = this.getMyUserId();
      if (!myId) return [];
      return list.filter(item => {
        const wid = this.getEntityId(item?.writer);
        return wid === myId;
      });
    }

    // Người không có role đặc thù: giữ nguyên
    return list;
  }

  enforceTitleLimit(e: Event) {
    const input = e.target as HTMLInputElement;
    if (input.value.length > this.titleMax) {
      input.value = input.value.slice(0, this.titleMax);
      this.contentForm.get('title')?.setValue(input.value, { emitEvent: false });
      this.contentForm.get('title')?.markAsTouched();
    }
  } 

  private quillRefs: Record<'body' | 'question' | 'answer', any> = {} as any;
  quillInstances: { [key: string]: any } = {};
  private findScrollParent(start: HTMLElement): HTMLElement {
    let el: HTMLElement | null = start;

    while (el && el !== this.document.body) {
      const style = getComputedStyle(el);
      const overflowY = style.overflowY;
      const canScroll = (overflowY === 'auto' || overflowY === 'scroll');

      if (canScroll && el.scrollHeight > el.clientHeight) {
        return el;       // chính là .modal-body trong case của anh
      }
      el = el.parentElement;
    }

    // fallback
    return (this.document.scrollingElement as HTMLElement)
        || this.document.documentElement
        || this.document.body;
  }

  onEditorCreated(quill: any, which: 'body' | 'question' | 'answer') {
    this.quillRefs[which] = quill;

    // Tắt spellcheck, Grammarly, LanguageTool...
    quill.root.setAttribute('spellcheck', 'false');
    quill.root.setAttribute('autocorrect', 'off');
    quill.root.setAttribute('autocomplete', 'off');
    quill.root.setAttribute('autocapitalize', 'off');

    quill.root.setAttribute('data-gramm', 'false');
    quill.root.setAttribute('data-enable-grammarly', 'false');
    quill.root.setAttribute('data-lt-active', 'false');

    // ===== NÚT FULLSCREEN TRÊN TOOLBAR =====
    const toolbarEl = quill.getModule('toolbar')?.container as HTMLElement | null;
    const scrollingContainer: HTMLElement = this.findScrollParent(quill.root as HTMLElement);
    if (toolbarEl) {
      // ====== GIỮ SCROLL KHI BẤM CÁC NÚT TOOLBAR (align, bold, list,...) ======
      if (scrollingContainer) {
        const keepScroll = () => {
          const prev = scrollingContainer.scrollTop;
          // để Quill xử lý xong rồi mới set lại scroll
          setTimeout(() => {
            scrollingContainer.scrollTop = prev;
          }, 0);
        };
        // dùng mousedown/touchstart để bắt mọi thao tác trên toolbar
        toolbarEl.addEventListener('mousedown', keepScroll);
        toolbarEl.addEventListener('touchstart', keepScroll);
      }
      // ========================================================================

      let right = toolbarEl.querySelector('.ql-formats-right') as HTMLSpanElement;
      if (!right) {
        right = document.createElement('span');
        right.className = 'ql-formats ql-formats-right';
        toolbarEl.appendChild(right);
      }
      if (!right.querySelector(`button.ql-fullscreen-${which}`)) {
        const btn = document.createElement('button');
        btn.type = 'button';
        btn.className = `ql-fullscreen-${which}`;
        btn.setAttribute('title', 'Toàn màn hình');

        const iconMax = `
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path d="M8 3H3v5M16 3h5v5M3 16v5h5M21 16v5h-5"
              fill="none" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round"/>
          </svg>`;
        const iconMin = `
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path d="M9 9H4V4m11 0h5v5M4 15v5h5m11-5v5h-5"
              fill="none" stroke="currentColor" stroke-width="2"
              stroke-linecap="round" stroke-linejoin="round"/>
          </svg>`;

        const setIcon = () => {
          const isOn =
            (which === 'body' && this.isBodyFS) ||
            (which === 'question' && this.isQuestionFS) ||
            (which === 'answer' && this.isAnswerFS);
          btn.innerHTML = isOn ? iconMin : iconMax;
        };
        setIcon();

        btn.addEventListener('click', () => {
          this.toggleFS(which);
          setIcon();
        });

        right.appendChild(btn);
      }
    }

    // ===== HANDLER IMAGE / VIDEO, DRAG & DROP, PASTE =====
    const toolbar = quill.getModule('toolbar');

    if (toolbar && typeof toolbar.addHandler === 'function') {
      // Image: mở file input + upload
      toolbar.addHandler('image', () => {
        const range =
          quill.getSelection(true) || { index: quill.getLength(), length: 0 };

        const input = this.createHiddenFileInput('image/*');
        input.onchange = () => {
          const file = input.files?.[0];
          if (file) this.uploadAndInsert(quill, file, range);
          this.document.body.removeChild(input);
        };
        input.click();
      });

      // Video: mở modal nhập URL
      toolbar.addHandler('video', () => {
        const btn: HTMLElement | null =
          (toolbar.container as HTMLElement).querySelector('button.ql-video');
        if (!btn) return;

        const range =
          quill.getSelection(true) || { index: quill.getLength(), length: 0 };
        (quill as any).__lastVideoRange = range;

        this.openVideoDropdown(btn, quill);
      });

      // ==== FIX NHẢY KHI CĂN LỀ ====
      toolbar.addHandler('align', (value: any) => {
        this.applyBlockFormat(quill, 'align', value);
      });

      // ==== FIX NHẢY KHI ĐỔI HEADER ====
      toolbar.addHandler('header', (value: any) => {
        this.applyBlockFormat(quill, 'header', value);
      });
    }

    // Kéo–thả ảnh
    const rootEl = quill.root as HTMLElement;
    rootEl.addEventListener('drop', (e: DragEvent) => {
      if (!e.dataTransfer?.files?.length) return;
      const file = Array.from(e.dataTransfer.files).find(f => f.type.startsWith('image/'));
      if (file) {
        e.preventDefault();
        e.stopPropagation();
        this.uploadAndInsert(quill, file);
      }
    });

    // Dán ảnh từ clipboard
    rootEl.addEventListener('paste', (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;
      const fileItem = Array.from(items).find(i => i.type.startsWith('image/'));
      if (fileItem) {
        e.preventDefault();
        const file = fileItem.getAsFile();
        if (file) this.uploadAndInsert(quill, file);
      }
    });

    // ===== GIỮ SCROLL KHI BẤM TOOLBAR & CHỈNH ẢNH =====

    // 1) tìm đúng scroll parent (thường là .cms-editor-modal .modal-body)
    const scrollContainer: HTMLElement = this.findScrollParent(quill.root as HTMLElement);

    if (scrollContainer) {
      // vị trí scroll ngay trước khi user thao tác
      let scrollBeforeAction = scrollContainer.scrollTop;

      const rememberScroll = () => {
        scrollBeforeAction = scrollContainer.scrollTop;
      };

      // khi user bắt đầu click / drag trong editor -> nhớ vị trí
      const rootEl = quill.root as HTMLElement;
      rootEl.addEventListener('mousedown', rememberScroll, true);

      // nếu có toolbar thì cũng nhớ trước khi bấm nút
      const toolbar = quill.getModule('toolbar');
      if (toolbar && toolbar.container) {
        toolbar.container.addEventListener('mousedown', rememberScroll, true);
      }

      const restoreScroll = () => {
        const target = scrollBeforeAction;
        // đợi Quill scroll xong rồi kéo về lại
        setTimeout(() => {
          scrollContainer.scrollTop = target;
        }, 0);
      };

      // 1) Bấm bất kỳ nút nào trên toolbar (align, bold, align ảnh...)
      if (toolbar && toolbar.container) {
        toolbar.container.addEventListener('click', () => {
          restoreScroll();
        });
      }

      // 2) Khi selection nhảy vào IMAGE (click/chọn ảnh, resize ảnh...)
      quill.on('selection-change', (range: any, oldRange: any, source: string) => {
        if (!range || source !== 'user') return;

        const [leaf] = quill.getLeaf(range.index);
        if (!leaf) return;

        const domNode = (leaf as any).domNode as HTMLElement | null;
        if (!domNode || domNode.tagName !== 'IMG') return; // chỉ khi focus vào ảnh

        restoreScroll();
      });
    }
  }

  private extractErrorMessage(
    err: any,
    defaultMsg: string,
    isCreate: boolean
  ): string {
    if (!err) return defaultMsg;

    const raw = (typeof err.error !== 'undefined') ? err.error : err;

    let msg: string | undefined;

    if (typeof raw === 'string') {
      try {
        const obj = JSON.parse(raw);
        msg = obj.detail || obj.message;
      } catch {
        msg = raw;
      }
    } else if (raw && typeof raw === 'object') {
      msg =
        raw.detail ||
        raw.message ||
        (Array.isArray(raw.non_field_errors) && raw.non_field_errors[0]) ||
        undefined;
    }

    if (!msg && err.status === 403) {
      msg = isCreate
        ? 'Tài khoản không có quyền tạo bài viết này.'
        : 'Tài khoản không có quyền cập nhật bài viết này.';
    }

    if (!msg && err.message) {
      msg = err.message;
    }

    if (!msg) msg = defaultMsg;

    return this.normalizeErrorText(msg);
  }

  private normalizeErrorText(msg: string): string {
    if (!msg) return msg;
    return msg
      .replace(/\s*\((GET|POST|PUT|PATCH|DELETE|HEAD|OPTIONS)\)\.?\s*$/i, '')
      .trim();
  }

  private readonly VIDEO_ACCEPT = ['video/mp4', 'video/webm', 'video/ogg'];
  private readonly VIDEO_MAX_MB = 200;

  private openVideoDropdown(anchor: HTMLElement, quill: any) {
    this.closeVideoDropdown();

    const menu = this.document.createElement('div');
    menu.className = 'ql-video-menu';
    menu.innerHTML = `
      <div class="ql-video-menu__item" data-action="url">Dán URL video…</div>
      <div class="ql-video-menu__item" data-action="upload">Tải từ máy…</div>
    `;

    const rect = anchor.getBoundingClientRect();
    Object.assign(menu.style, {
      position: 'fixed',
      top: rect.bottom + 6 + 'px',
      left: rect.left + 'px',
      zIndex: '10000'
    } as CSSStyleDeclaration);

    menu.addEventListener('click', (e) => {
      const el = e.target as HTMLElement;
      const action = el.getAttribute('data-action');
      if (action === 'url') {
        this.closeVideoDropdown();
        this.openVideoUrlModal(quill);
      }
      if (action === 'upload') {
        this.closeVideoDropdown();
        this.pickVideoFile(quill);
      }
    });

    const onDocClick = (ev: MouseEvent) => {
      if (!menu.contains(ev.target as Node) && ev.target !== anchor) {
        this.closeVideoDropdown();
        this.document.removeEventListener('click', onDocClick, true);
      }
    };
    setTimeout(() => this.document.addEventListener('click', onDocClick, true));

    this.document.body.appendChild(menu);
    this.currentVideoMenu = menu;
  }

  private closeVideoDropdown() {
    if (this.currentVideoMenu?.parentNode) {
      this.currentVideoMenu.parentNode.removeChild(this.currentVideoMenu);
    }
    this.currentVideoMenu = undefined;
  }

  private pickVideoFile(quill: any) {
    const input = this.createHiddenFileInput('video/*');
    input.onchange = () => {
      const file = input.files?.[0];
      if (file) this.uploadAndInsertVideo(quill, file);
      this.document.body.removeChild(input);
    };
    input.click();
  }

  private validateVideo(file: File) {
    if (!this.VIDEO_ACCEPT.includes(file.type)) {
      this._toastSerive.error('Định dạng video không hợp lệ. Chỉ MP4/WebM/OGG', 'Lỗi');
      return false;
    }
    if (file.size > this.VIDEO_MAX_MB * 1024 * 1024) {
      this._toastSerive.error(`Video quá lớn (>${this.VIDEO_MAX_MB}MB)`, 'Lỗi');
      return false;
    }
    return true;
  }

  private insertVideoToEditor(
    quill: any,
    url: string,
    range?: { index: number; length: number }
  ) {
    const sc: HTMLElement = quill.scrollingContainer || quill.root.parentElement;
    const prevScrollTop = sc ? sc.scrollTop : null;

    const curRange = range || quill.getSelection(true) || { index: quill.getLength(), length: 0 };

    quill.insertEmbed(curRange.index, 'html-video', url, 'user');
    quill.setSelection(curRange.index + 1);

    if (sc != null && prevScrollTop != null) {
      setTimeout(() => {
        sc.scrollTop = prevScrollTop;
      }, 0);
    }
  }

  private uploadAndInsertVideo(quill: any, file: File) {
    if (!this.validateVideo(file)) return;
    this._toastSerive.info('Đang tải video...', 'Upload');

    // lấy range đã lưu khi bấm nút video
    const range = (quill as any).__lastVideoRange
      || quill.getSelection(true)
      || { index: quill.getLength(), length: 0 };

    this._cmsService.uploadCmsAsset(file).pipe(
      catchError(() => {
        this._toastSerive.error('Upload video thất bại', 'Lỗi');
        return of(null);
      })
    ).subscribe(res => {
      if (!res?.url) return;
      this.insertVideoToEditor(quill, res.url, range);  // truyền range
      this._toastSerive.success('Tải video thành công', 'Thành công');
    });
  }

  private normalizeHtmlFromBackend(html: any): string {
    // Không đụng chạm, trả về nguyên trạng
    return (html ?? '').toString();
  }


  private openVideoUrlModal(quill: any) {
    this.tmpVideoUrl = '';
    this.quillWaitingUrl = quill;
    this._modalService.open(this.VideoUrlModal, { centered: true, size: 'md' });
  }

  public confirmVideoUrl(modalRef: any) {
    const raw = (this.tmpVideoUrl || '').trim();
    if (!raw) return;

    const quill = this.quillWaitingUrl;
    this.quillWaitingUrl = null;

    const iframeSrc = this.normalizeVideoUrlForIframe(raw);
    if (iframeSrc) {
      this.insertIframeToEditor(quill, iframeSrc);
    } else {
      this.insertVideoToEditor(quill, raw);
    }
    modalRef.close();
  }

  private createHiddenFileInput(accept = 'image/*') {
    const input = this.document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.style.display = 'none';
    this.document.body.appendChild(input);
    return input;
  }

  private validateImage(file: File) {
    if (!this.IMG_ACCEPT.includes(file.type)) {
      this._toastSerive.error('Định dạng ảnh không hợp lệ. Chỉ hỗ trợ PNG/JPEG/WebP/GIF', 'Lỗi');
      return false;
    }
    if (file.size > this.IMG_MAX_MB * 1024 * 1024) {
      this._toastSerive.error(`Ảnh quá lớn (>${this.IMG_MAX_MB}MB)`, 'Lỗi');
      return false;
    }
    return true;
  }
  private applyBlockFormat(
    quill: any,
    format: 'align' | 'header' | 'list' | 'indent',
    value: any
  ) {
    // Tìm scrolling container
    const sc: HTMLElement =
      (quill.scrollingContainer as HTMLElement) || quill.root.parentElement;

    const prevScrollTop = sc ? sc.scrollTop : null;
    const range = quill.getSelection(true) || { index: quill.getLength(), length: 0 };

    // Áp dụng format giống handler mặc định
    quill.format(format, value);

    // Giữ nguyên selection (mặc định đôi khi set về đầu block)
    if (range) {
      quill.setSelection(range.index, range.length, 'user');
    }

    // Khôi phục scroll sau khi Quill xử lý xong
    if (sc && prevScrollTop != null) {
      setTimeout(() => {
        sc.scrollTop = prevScrollTop;
      }, 0);
    }
  }

  private insertImageToEditor(
    quill: any,
    url: string,
    range?: { index: number; length: number }
  ) {
    const sc: HTMLElement = quill.scrollingContainer || quill.root.parentElement;
    const prevScrollTop = sc ? sc.scrollTop : null;

    const curRange = range || quill.getSelection(true) || { index: quill.getLength(), length: 0 };

    quill.insertEmbed(curRange.index, 'image', url, 'user');
    quill.setSelection(curRange.index + 1);

    // RESTORE SCROLL SAU KHI CHÈN
    if (sc != null && prevScrollTop != null) {
      setTimeout(() => {
        sc.scrollTop = prevScrollTop;
      }, 0);
    }
  }

  private uploadAndInsert(quill: any, file: File, range?: { index: number; length: number }) {
    if (!this.validateImage(file)) return;
    this._toastSerive.info('Đang tải ảnh...', 'Upload');

    this._cmsService.uploadCmsAsset(file).pipe(
      catchError(err => {
        this._toastSerive.error('Upload ảnh thất bại', 'Lỗi');
        return of(null);
      })
    ).subscribe(res => {
      if (!res?.url) return;
      this.insertImageToEditor(quill, res.url, range); // TRUYỀN RANGE
      this._toastSerive.success('Tải ảnh thành công', 'Thành công');
    });
  }

  private lockBodyScroll(lock: boolean) {
    if (lock) this.renderer.addClass(this.document.body, 'editor-fs-lock');
    else this.renderer.removeClass(this.document.body, 'editor-fs-lock');
  }

  private exitAllFS() {
    this.isBodyFS = false;
    this.isQuestionFS = false;
    this.isAnswerFS = false;
    this.lockBodyScroll(false);
    setTimeout(() => feather.replace(), 0);
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscKey(e: KeyboardEvent) {
    if (this.isBodyFS || this.isQuestionFS || this.isAnswerFS) {
      e.preventDefault();
      e.stopPropagation();
      this.exitAllFS();
    }
  }

  toggleFS(which: 'body'|'question'|'answer') {
    if (which === 'body') this.isBodyFS = !this.isBodyFS;
    if (which === 'question') this.isQuestionFS = !this.isQuestionFS;
    if (which === 'answer') this.isAnswerFS = !this.isAnswerFS;

    const anyFS = this.isBodyFS || this.isQuestionFS || this.isAnswerFS;
    this.lockBodyScroll(anyFS);

    setTimeout(() => feather.replace(), 0);
  }

  private iAmWriter(): boolean {
    const myId = this.getMyUserId();
    if (!myId) return false;
    return (this.writerUsers || []).some(
      (u: any) => (u?.user?.id ?? u?.user_info?.id) === myId
    );
  }

  formatRelativeTime = formatRelativeTime;

  // Phương thức xử lý Ctrl+Click để select thẻ
  onWorkItemClick(event: MouseEvent, item: any) {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      event.stopPropagation();
      this.toggleWorkSelection(item);
    } else {
      // Click thường để mở modal
      this.modalOpen(this.modalContent, item);
    }
  }

  // Toggle selection của một thẻ
  toggleWorkSelection(item: any) {
    const index = this.selectedWork.findIndex(
      work => work.id === item.id && work.class === item.class
    );
    if (index > -1) {
      this.selectedWork.splice(index, 1);
    } else {
      this.selectedWork.push(item);
    }
  }

  // Kiểm tra xem thẻ có được chọn không
  isWorkSelected(item: any): boolean {
    return this.selectedWork.some(
      work => work.id === item.id && work.class === item.class
    );
  }

  // Clear tất cả selection
  clearWorkSelection() {
    this.selectedWork = [];
  }

  // Mở modal gán Writer
  openAssignWriterModal() {
    if (this.selectedWork.length === 0) return;
    this.openAssignModal('writer');
  }

  // Mở modal gán Reviewer
  openAssignReviewerModal() {
    if (this.selectedWork.length === 0) return;
    this.openAssignModal('reviewer');
  }

  // Mở modal gán chung
  private openAssignModal(type: 'writer' | 'reviewer') {
    this.assignModalType = type;
    this.selectedUserId = null; // Reset user selection
    this.assignModalRef = this._modalService.open(this.AssignModal, { 
      centered: true, 
      size: 'md' 
    });
  }

  // Gán Writer/Reviewer cho các thẻ đã chọn
  assignUserToSelectedWork(userId: number) {
    if (this.selectedWork.length === 0) return;

    // Phân loại selectedWork theo class
    const contentIds = this.selectedWork.filter(work => work.class === 'content').map(work => work.id);
    const qaIds = this.selectedWork.filter(work => work.class === 'qa').map(work => work.id);

    // Xác định param cho API
    const roleField = this.assignModalType;
    const apiPromises: Promise<any>[] = [];

    if (contentIds.length > 0) {
      const params: any = { ids: contentIds };
      params[roleField] = userId;
      apiPromises.push(
        this._cmsService.bulkContentUpdateRoles(params).toPromise()
      );
    }
    if (qaIds.length > 0) {
      const params: any = { ids: qaIds };
      params[roleField] = userId;
      apiPromises.push(
        this._cmsService.bulkQaUpdateRoles(params).toPromise()
      );
    }

    Promise.all(apiPromises).then((results) => {
      this.createdOrUpdated.emit(results);
      this._modalService.dismissAll();
      this._toastSerive.success(
        `Đã gán ${this.selectedWork.length} thẻ.`,
        'Thành công'
      );
    }).catch(err => {
      this._toastSerive.error('Có lỗi xảy ra khi gán người dùng.', 'Lỗi');
    });
  }

  filterBySelectedTypes(list: any[]): any[] {
    if (!this.selectedWorkTypes || this.selectedWorkTypes.length === 0) return list;
    return list.filter(item => this.selectedWorkTypes.includes(item.class));
  }

  private applySearch(list: any[]): any[] {
    const qNorm = this.normalizeSearchText(this.workSearchTerm);
    if (!qNorm) return list;

    return list.filter(item => this.matchSearch(item, qNorm));
  }

  private normalizeSearchText(input: any): string {
    if (input == null) return '';

    return input
      .toString()
      .toLowerCase()
      // tách tổ hợp thành base + dấu
      .normalize('NFD')
      // bỏ hết dấu (kể cả các dấu tổ hợp như ̣, ̉,...)
      .replace(/[\u0300-\u036f]/g, '')
      // đổi non-breaking space & các loại space lạ về space thường
      .replace(/\u00A0/g, ' ')
      // gom nhiều khoảng trắng liên tiếp thành 1 space
      .replace(/\s+/g, ' ')
      .trim();
  }

  private matchSearch(item: any, qNorm: string): boolean {
    const titleNorm = this.normalizeSearchText(item?.title);
    if (!titleNorm) return false;
    return titleNorm.includes(qNorm);
  }

  parseListWork() {
    this._listDraft = [];
    this._listWriting = [];
    this._listReviewing = [];
    this._listRejected = [];
    this._listCompleted = [];
    this._listPublished = [];
    this._listUnpublished = [];
    this._listArchived = [];

    if (!this.cmsData) return;

    const keys = ['content', 'qa'];
    for (const key of keys) {
      const dataArr = this.cmsData[key];
      if (!Array.isArray(dataArr)) continue;

      for (const itemOriginal of dataArr) {
        const item = { ...itemOriginal, class: key };
        switch (item.status) {
          case 'DRAFT':
            this._listDraft.push(item);
            break;
          case 'WRITING':
            this._listWriting.push(item);
            break;
          case 'REVIEWING':
            this._listReviewing.push(item);
            break;
          case 'REJECTED':
            this._listRejected.push(item);
            break;
          case 'COMPLETED':
            this._listCompleted.push(item);
            break;
          case 'PUBLISHED':
            this._listPublished.push(item);
            break;
          case 'UNPUBLISHED':
            this._listUnpublished.push(item);
            break;
          case 'ARCHIVED':
            this._listArchived.push(item);
            break;
        }
      }
    }
  }

  private isAllowedTransition(from: string, to: string): boolean {
    if (this.isAdmin) return true;
    if (from === to) return true;
    if (this.isWriter && !this.isReviewer) {
      return (
        (from === 'DRAFT'    && to === 'WRITING')   ||
        (from === 'DRAFT'    && to === 'REVIEWING')   ||
        (from === 'WRITING'  && to === 'REVIEWING') ||
        (from === 'WRITING'  && to === 'DRAFT')     ||
        (from === 'REVIEWING' && to === 'WRITING')  ||
        (from === 'REVIEWING' && to === 'DRAFT')
      );
    }

    if (this.isReviewer && !this.isWriter) {
      return true;
    }

    if (this.isWriter && this.isReviewer) {
      return (
        // Writer
        (from === 'DRAFT'    && to === 'WRITING')   ||
        (from === 'WRITING'  && to === 'REVIEWING') ||
        (from === 'WRITING'  && to === 'DRAFT')     || 
        (from === 'REVIEWING' && to === 'WRITING')  ||
        (from === 'REVIEWING' && to === 'DRAFT')    ||

        // Reviewer
        (from === 'REVIEWING' && (to === 'COMPLETED' || to === 'REJECTED' || to === 'ARCHIVED')) ||
        (from === 'COMPLETED' && to === 'PUBLISHED') ||

        // Đưa về REVIEWING
        (from === 'COMPLETED' && to === 'REVIEWING') ||
        (from === 'REJECTED'  && to === 'REVIEWING') ||
        (from === 'ARCHIVED'  && to === 'REVIEWING') ||
        (from === 'PUBLISHED' && to === 'REVIEWING')
      );
    }

    return false;
  }

  trackingDragDrop() {
    this.dragSub?.unsubscribe();
    this.dropModelSub?.unsubscribe();

    let originalParent: HTMLElement = null;
    let originalNextSibling: Element = null;

    this.dragSub = this._dragulaService.drag(ListWorkComponent.DRAG_GROUP)
      .subscribe(({ el, source }) => {
        originalParent = el.parentNode as HTMLElement;
        originalNextSibling = el.nextElementSibling;

        this.isDragging   = true;
        this.dragFromColId = (source as HTMLElement)?.id || null;
        this.dragFromStatus = ListWorkComponent.STATUS_MAP[this.dragFromColId!] || null;
        this.renderer.addClass(this.document.body, 'kanban-dragging');
        if (this.dragFromColId) this.computeAllowedTargets(this.dragFromColId);
        this.cdr.markForCheck();
      });

    this._dragulaService.over(ListWorkComponent.DRAG_GROUP)
      .subscribe(({ container }) => {
        this.hoverColId = (container as HTMLElement)?.id || null;
        this.cdr.markForCheck();
      });

    this._dragulaService.out(ListWorkComponent.DRAG_GROUP)
      .subscribe(() => {
        this.hoverColId = null;
        this.cdr.markForCheck();
      });

    const _clear = () => {
      this.clearDragHints();
      this.renderer.removeClass(this.document.body, 'kanban-dragging');
    };
    this._dragulaService.drop(ListWorkComponent.DRAG_GROUP).subscribe(_clear);
    this._dragulaService.dragend(ListWorkComponent.DRAG_GROUP).subscribe(_clear);
    this._dragulaService.cancel(ListWorkComponent.DRAG_GROUP).subscribe(_clear);


    this.dropModelSub = this._dragulaService.dropModel(ListWorkComponent.DRAG_GROUP)
      .subscribe(({el, target, source, item, sourceModel, targetModel, sourceIndex}) => {
        const newStatus = ListWorkComponent.STATUS_MAP[target.id];
        const oldStatus = ListWorkComponent.STATUS_MAP[source.id];
        if (!newStatus || newStatus === oldStatus) {
          this.clearDragHints();
          return;
        }
        if (!this.isAllowedTransition(oldStatus, newStatus)) {
          this._toastSerive.warning('Bạn không có quyền thực hiện chuyển trạng thái này.', 'Không được phép');
          const idxInTarget = targetModel.indexOf(item);
          if (idxInTarget >= 0) targetModel.splice(idxInTarget, 1);
          sourceModel.splice(sourceIndex, 0, item);
          setTimeout(() => {
            if (originalParent) {
              if (originalNextSibling) originalParent.insertBefore(el, originalNextSibling);
              else originalParent.appendChild(el);
            }
          }, 0);
          this.clearDragHints(); 
          return;
        }

        this.updateItemStatusAPI(item, newStatus).subscribe(
          (res) => {
            if (this.cmsData) {
              const key = item.class as 'content'|'qa';
              const list = this.cmsData[key];
              const idx = Array.isArray(list) ? list.findIndex(i => i.id === item.id) : -1;
              if (idx !== -1) this.cmsData[key][idx] = { ...res, class: key };
            }
            this.parseListWork();
            this._toastSerive.success('Đã cập nhật trạng thái.', 'Thành công');
          },
          (err) => {
            // console.error('update status error', err);

            const msg = this.extractErrorMessage(
              err,
              'Không thể cập nhật trạng thái.',
              false
            );

            this._toastSerive.error(msg, 'Lỗi');

            const idxInTarget = targetModel.indexOf(item);
            if (idxInTarget >= 0) targetModel.splice(idxInTarget, 1);
            sourceModel.splice(sourceIndex, 0, item);
            setTimeout(() => {
              if (originalParent) {
                if (originalNextSibling) originalParent.insertBefore(el, originalNextSibling);
                else originalParent.appendChild(el);
              }
            }, 0);
          }
        );
        this.clearDragHints();
      });
  }

  updateItemStatusAPI(item: any, status: string) {
    if (item.class === 'content') {
      return this._cmsService.updateContent(item.id, { status });
    } else if (item.class === 'qa') {
      return this._cmsService.updateQa(item.id, { status });
    }

    return {
      subscribe: (_s, e) => e && e()
    };
  }

  modalOpen(modal, data: any = null, size: string = 'lg') {
    this.resetUIState();
    this.slugWarnedOnce = false;

    this.resetContentForm();

    if (data) {
      const toId = (u: any) =>
        (u && typeof u === 'object') ? (u.id ?? u.user?.id ?? u.user_info?.id ?? null) : (u ?? null);

      this.contentForm.patchValue({
        class: data.class || null,
        id: data.id || null,
        title: data.title || '',

        // Content
        body: ('body' in data)
          ? this.normalizeHtmlFromBackend(data.body)
          : '',
        description: ('description' in data)
          ? this.normalizeHtmlFromBackend(data.description)
          : '',
        type: data.type || 'NORMAL',

        // QA
        question: ('question' in data)
          ? this.normalizeHtmlFromBackend(data.question)
          : '',
        answer: ('answer' in data)
          ? this.normalizeHtmlFromBackend(data.answer)
          : '',

        // Common
        slug: data.slug || '',
        keywords: data.keywords || '',
        publish_date: data.publish_date ? String(data.publish_date).slice(0, 10) : '',
        topic: data.topic || '',
        writer: toId(data.writer),
        reviewer: toId(data.reviewer),
        admin: toId(data.admin),
      });

      if (data.id) this.contentForm.get('class')?.disable();
      else this.contentForm.get('class')?.enable();

      this.existingImageUrl = (data.image_url || data.image || null) as string | null;
      this.thumbPreviewUrl  = this.existingImageUrl || null;
    } else {
      this.contentForm.get('class')?.enable();
      this.existingImageUrl = null;
      this.thumbPreviewUrl  = null;
    }

    this.setImageValidators();
    this.rebuildSlugIndex();

    const cls = this.contentForm.get('class')?.value;
    const curSlug = this.contentForm.get('slug')?.value || '';
    const curId = Number(this.contentForm.get('id')?.value) || null;
    // if (!curId && cls === 'content' && !curSlug) {
    //   const base = this.vnSlugify(this.contentForm.get('title')?.value || '');
    //   const unique = this.suggestUniqueSlug(base, null);
    //   this.contentForm.get('slug')?.setValue(unique, { emitEvent: false });
    // }
    if (this.AUTO_SLUG_FROM_TITLE && !curId && cls === 'content' && !curSlug) {
      const base = this.vnSlugify(this.contentForm.get('title')?.value || '');
      const unique = this.suggestUniqueSlug(base, null);
      this.contentForm.get('slug')?.setValue(unique, { emitEvent: false });
    }
    // 3) mở modal & giữ ref
    this.modalRef = this._modalService.open(modal, {
      centered: true,
      size,
      backdrop: 'static',
      keyboard: false,
      windowClass: 'cms-editor-modal',
    });

    // 4) khi đóng hoặc dismiss → reset sạch sẽ
    this.modalRef.closed.subscribe(() => {
      this.resetUIState();
      this.resetContentForm();
    });
    this.modalRef.dismissed.subscribe(() => {
      this.resetUIState();
      this.resetContentForm();
    });

    setTimeout(() => feather.replace(), 0);
  }

  resetContentForm() {
    this.contentForm.reset(this.defaultForm());
    this.contentForm.markAsPristine();
    this.contentForm.markAsUntouched();
    this.contentForm.get('publish_date')?.enable({ emitEvent: false });
  }

  private buildQaPayload(v: any) {
    return {
      title: v.title ?? '',
      topic: v.topic ?? '',
      question: v.question ?? '',
      answer: v.answer ?? '',
      writer: v.writer ?? null,
      reviewer: v.reviewer ?? null
    };
  }

  submitContent() {
    this.setImageValidators();

    if (this.contentForm.invalid) {
      this.contentForm.markAllAsTouched();
      this._toastSerive.error('Vui lòng nhập đủ thông tin bắt buộc.', 'Thiếu dữ liệu');
      return;
    }

    const v = this.contentForm.getRawValue();
    let apiCall$;

    if (v.class === 'content') {
      const fd = new FormData();
      fd.append('title', v.title ?? '');
      fd.append('topic', v.topic ?? '');
      if (v.slug)        fd.append('slug', v.slug);
      if (v.keywords)    fd.append('keywords', v.keywords);
      if (v.body)        fd.append('body', v.body);
      if (v.description) fd.append('description', v.description);
      if (v.type)       fd.append('type', v.type);
      if (v.publish_date) fd.append('publish_date', v.publish_date);
      if (v.writer)      fd.append('writer', String(v.writer));
      if (v.reviewer)    fd.append('reviewer', String(v.reviewer));
      const isFile = v.image instanceof File || (window as any).File && v.image instanceof (window as any).File;
      if (isFile) {
        fd.append('image', v.image as File);
      }

      apiCall$ = v.id
        ? this._cmsService.updateContent(v.id, fd)
        : this._cmsService.createContent(fd);

    } else if (v.class === 'qa') {
      const payload = this.buildQaPayload(v);

      apiCall$ = v.id
        ? this._cmsService.updateQa(v.id, payload)
        : this._cmsService.createQa(payload);
    } else {
      return;
    }

    apiCall$.subscribe(
      (res) => {
        const key = v.class as 'content' | 'qa';

        if (this.cmsData) {
          if (!Array.isArray(this.cmsData[key])) {
            this.cmsData[key] = [];
          }

          if (v.id) {
            const idx = this.cmsData[key].findIndex((i: any) => i.id === v.id);
            if (idx !== -1) {
              this.cmsData[key][idx] = { ...res, class: key };
            } else {
              this.cmsData[key].unshift({ ...res, class: key });
            }
          } else {
            this.cmsData[key].unshift({ ...res, class: key });
          }

          this.parseListWork();
        }

        this.resetContentForm();
        this._modalService.dismissAll();
        this.createdOrUpdated.emit(res);

        this._toastSerive.success(
          v.id ? 'Đã cập nhật công việc.' : 'Đã tạo công việc.',
          'Thành công'
        );
      },
      (err) => {
        // console.error('submitContent error', err);

        const defaultMsg = v.id
          ? 'Đã xảy ra lỗi khi cập nhật công việc.'
          : 'Đã xảy ra lỗi khi tạo công việc.';

        const msg = this.extractErrorMessage(err, defaultMsg, !v.id);

        this._toastSerive.error(msg, 'Lỗi');
      }
    );
  }
  private vnSlugify(input: string): string {
    const s = (input || '').normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    return s
      .replace(/đ/g, 'd').replace(/Đ/g, 'D')
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  private slugIndex: Map<string, Set<number>> = new Map();

  private rebuildSlugIndex() {
    this.slugIndex.clear();
    const list = (this.cmsData?.content || []) as any[];
    for (const item of list) {
      const id = Number(item?.id);
      const raw = String(item?.slug || '');
      const slug = this.vnSlugify(raw || String(item?.title || ''));
      if (!slug) continue;
      if (!this.slugIndex.has(slug)) this.slugIndex.set(slug, new Set());
      this.slugIndex.get(slug)!.add(id);
    }
  }

  private isSlugTaken(slug: string, excludeId?: number | null): boolean {
    const set = this.slugIndex.get(slug);
    if (!set || set.size === 0) return false;
    if (excludeId == null) return true;
    return !(set.size === 1 && set.has(excludeId));
  }

  private suggestUniqueSlug(base: string, excludeId?: number | null): string {
    let slug = base || 'post';
    if (!this.isSlugTaken(slug, excludeId)) return slug;
    let i = 2;
    while (this.isSlugTaken(`${slug}-${i}`, excludeId)) i++;
    return `${slug}-${i}`;
  }

  // === Helpers hiển thị lỗi trong template ===
  hasError(ctrlName: string, err?: string): boolean {
    const c = this.contentForm.get(ctrlName);
    if (!c) return false;
    if (err) return c.touched && c.hasError(err);
    return c.touched && c.invalid;
  }

  firstError(ctrlName: string): string | null {
    const c = this.contentForm.get(ctrlName);
    if (!c || !c.errors) return null;
    // Ưu tiên thứ tự thông báo
    if (c.errors['required'])  return 'Trường này là bắt buộc.';
    if (c.errors['pastDate'])  return 'Không được chọn ngày trong quá khứ.';
    if (c.errors['slugTaken']) return 'Slug đã tồn tại.';
    if (c.errors['maxlength']) return 'Vượt quá độ dài cho phép.';
    return 'Giá trị không hợp lệ.';
  }

  // Nhận gợi ý slug khi user muốn
  applySlugSuggestion() {
    const ctrl = this.contentForm.get('slug');
    const sug = ctrl?.getError('suggest');
    if (ctrl && sug) {
      ctrl.setValue(sug);
      ctrl.setErrors(null);
      ctrl.markAsDirty();
      ctrl.markAsTouched();
    }
  }

  private existingImageUrl: string | null = null;

  private setImageValidators() {
    const cls = this.contentForm.get('class')?.value;
    const hasId = !!this.contentForm.get('id')?.value;
    const imgCtrl = this.contentForm.get('image');

    const mustRequireImage = (cls === 'content') && (!hasId || !this.existingImageUrl);

    if (mustRequireImage) {
      imgCtrl?.setValidators([Validators.required]);
    } else {
      imgCtrl?.clearValidators();
    }
    imgCtrl?.updateValueAndValidity({ emitEvent: false });
  }

  closeModal() {
    this._modalService.dismissAll();
    this.reloadCmsUser.emit();
  }

  ngOnInit(): void {
    this.isSuperAdmin = this._auth.currentUserValue.role == 'SUPER_ADMIN';
    this._cmsService.ensureCmsRolesLoaded().subscribe(roles => {
      this.myRoles   = roles || [];
      this.isWriter   = this.myRoles.includes('WRITER');
      this.isReviewer = this.myRoles.includes('REVIEWER');
      this.isAdmin    = this.myRoles.includes('ADMIN');
      this.setImageValidators();
      this.buildWriterOptions();

      const opts = {
        accepts: (el: Element, target: Element, source: Element) => {
          if (source === target) return true;
          const to   = ListWorkComponent.STATUS_MAP[(target as HTMLElement)?.id || ''];
          const from = ListWorkComponent.STATUS_MAP[(source as HTMLElement)?.id || ''];
          if (!to || !from) return true;
          return this.isAllowedTransition(from, to);
        }
      };

      this.ensureDragulaGroup(opts);
      this.trackingDragDrop(); 

      // try {
      //   (this._dragulaService as any).setOptions('multiple-list-group', opts);
      // } catch {
      //   this._dragulaService.createGroup('multiple-list-group', opts);
      // }
    });

    // 2) Helper: YYYY-MM-DD theo local time
    const toISODate = (d: Date) => {
      const tz = d.getTimezoneOffset();
      const local = new Date(d.getTime() - tz * 60000);
      return local.toISOString().slice(0, 10);
    };

    // 3) Validator: cho phép rỗng, nhưng nếu có giá trị thì phải >= hôm nay
    const notPastDate = (control: any) => {
      const v: string = control?.value;
      if (!v) return null; 
      const today = toISODate(new Date()); 
      return v < today ? { pastDate: true } : null;
    };

    this.contentForm.get('class')?.valueChanges.subscribe(val => {
      const bodyCtrl = this.contentForm.get('body');
      const qCtrl    = this.contentForm.get('question');
      const aCtrl    = this.contentForm.get('answer');
      const slugCtrl = this.contentForm.get('slug');
      const kwCtrl   = this.contentForm.get('keywords');
      const pub      = this.contentForm.get('publish_date');

      if (val === 'qa') {
        pub?.reset();
        pub?.disable({ emitEvent: false });
        pub?.clearValidators();
        pub?.updateValueAndValidity({ emitEvent: false });
      } else if (val === 'content') {
        pub?.enable({ emitEvent: false });
        pub?.setValidators([notPastDate]);               // <-- CHẶN ngày quá khứ
        pub?.updateValueAndValidity({ emitEvent: false });
      } else {
        pub?.enable({ emitEvent: false });
        pub?.clearValidators();
        pub?.updateValueAndValidity({ emitEvent: false });
      }

      if (val === 'content') {
        bodyCtrl?.setValidators([Validators.required]);
        qCtrl?.clearValidators();
        aCtrl?.clearValidators();
        slugCtrl?.setValidators([Validators.required]);
        kwCtrl?.setValidators([Validators.required]);
      } else if (val === 'qa') {
        qCtrl?.setValidators([Validators.required]);
        aCtrl?.setValidators([Validators.required]);
        bodyCtrl?.clearValidators();
        slugCtrl?.clearValidators();
        kwCtrl?.clearValidators();
        slugCtrl?.setErrors(null);
        kwCtrl?.setErrors(null);
      } else {
        bodyCtrl?.clearValidators();
        qCtrl?.clearValidators();
        aCtrl?.clearValidators();
        slugCtrl?.clearValidators();
        kwCtrl?.clearValidators();
      }

      bodyCtrl?.updateValueAndValidity({ emitEvent: false });
      qCtrl?.updateValueAndValidity({ emitEvent: false });
      aCtrl?.updateValueAndValidity({ emitEvent: false });
      slugCtrl?.updateValueAndValidity({ emitEvent: false });
      kwCtrl?.updateValueAndValidity({ emitEvent: false });
    });
    if (this.AUTO_SLUG_FROM_TITLE) {
      this.contentForm.get('title')?.valueChanges.subscribe((val: string) => {
        const slugCtrl = this.contentForm.get('slug');
        const id = Number(this.contentForm.get('id')?.value) || null;
        if (!slugCtrl) return;
        if (!slugCtrl.value) {
          const base = this.vnSlugify(val || '');
          const unique = this.suggestUniqueSlug(base, id);
          slugCtrl.setValue(unique, { emitEvent: false });
          slugCtrl.markAsDirty();
        }
      });
    }

    this.contentForm.get('slug')?.valueChanges.subscribe((val: string) => {
      const slugCtrl = this.contentForm.get('slug');
      const id = Number(this.contentForm.get('id')?.value) || null;
      if (!slugCtrl) return;

      const norm = this.vnSlugify(val || '');
      if (val !== norm) {
        slugCtrl.setValue(norm, { emitEvent: false });
      }

      if (!norm) {
        slugCtrl.setErrors({ required: true });
        return;
      }

      if (this.isSlugTaken(norm, id)) {
        const sug = this.suggestUniqueSlug(norm, id);
        // KHÔNG auto thay đổi, gắn lỗi + gợi ý để UI hiển thị viền đỏ & message
        slugCtrl.setErrors({ slugTaken: true, suggest: sug });
        // Toast vẫn hiện 1 lần
        if (!this.slugWarnedOnce) {
          this._toastSerive.warning('Slug bị trùng, đã có gợi ý phía dưới.', 'Cảnh báo');
          this.slugWarnedOnce = true;
        }
      } else {
        slugCtrl.setErrors(null);
        this.slugWarnedOnce = false;
      }
    });

    const initClass = this.contentForm.get('class')?.value;
    if (initClass != null) {
      this.contentForm.get('class')?.setValue(initClass);
    }
  }


  ngOnChanges(changes: SimpleChanges): void {
    if ('cmsData' in changes) {
      if (this.cmsData) {
        this.parseListWork();
        this.rebuildSlugIndex();
        this.trackingDragDrop();
      }
    }
    if ('writerUsers' in changes) {
      this.buildWriterOptions();
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.dragSub?.unsubscribe();
    this.dropModelSub?.unsubscribe();
    this.destroyDragulaGroupIfAny();  
  }

}
