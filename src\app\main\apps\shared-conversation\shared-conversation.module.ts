import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { CoreCommonModule } from '@core/common.module';
import { PipeModule } from "app/layout/components/pipe/pipe.module";

import { SharedConversationComponent } from './shared-conversation.component';
import { SharedConversationHeaderComponent } from './components/shared-conversation-header/shared-conversation-header.component';
import { SharedConversationContentComponent } from './components/shared-conversation-content/shared-conversation-content.component';
import { SharedConversationFooterComponent } from './components/shared-conversation-footer/shared-conversation-footer.component';

const routes: Routes = [
  { path: ':scid', component: SharedConversationComponent },
  { path: '**', redirectTo: '/pages/miscellaneous/error' }
];

@NgModule({
  declarations: [
    SharedConversationComponent,
    SharedConversationHeaderComponent,
    SharedConversationContentComponent,
    SharedConversationFooterComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    NgbModule,
    NgSelectModule,
    NgxDatatableModule,
    CoreCommonModule,
    PipeModule,
  ]
})
export class SharedConversationModule {}
