<div class="landing">
  <!-- Header Section -->
  <app-landing-header
    [activeSection]="activeSection"
    [showNav]="true"
    [logoSrc]="'assets/images/logo/COpenAIlogo.svg'"
    brandText="CLS"
    (sectionSelect)="scrollTo($event)">
  </app-landing-header>
  <div
    class="landing__drawer-backdrop"
    *ngIf="isMenuOpen"
    (click)="closeMenu()">
  </div>
  <aside
    id="landing-drawer"
    class="landing__drawer"
    [class.open]="isMenuOpen"
    (keydown.escape)="closeMenu()"
    tabindex="-1">
    <div class="landing__drawer-header">
      <span class="landing__drawer-title">Danh mục</span>
      <button type="button" class="landing__drawer-close" aria-label="Đóng menu" (click)="closeMenu()">✕</button>
    </div>
    <nav class="landing__drawer-nav" role="navigation" aria-label="<PERSON><PERSON><PERSON><PERSON> hướng di động">
      <ul class="landing__drawer-list">
        <li class="landing__drawer-item" (click)="navTo('chatbot')">Truy vấn</li>
        <li class="landing__drawer-item" (click)="navTo('information')">Thông tin</li>
        <li class="landing__drawer-item" (click)="navTo('mission')">Sứ mệnh</li>
        <li class="landing__drawer-item" (click)="navTo('articles')">Bài viết</li>
        <li class="landing__drawer-item" (click)="gotoQaLegal()">QA - Legal</li>
        <li class="landing__drawer-item" (click)="navTo('contact')">Liên hệ</li>
        <li class="landing__drawer-item landing__drawer-login" (click)="gotoLogin()">Đăng nhập</li>
      </ul>
    </nav>
  </aside>
  <!-- Main Section -->
  <main class="landing__main">
    <!-- Sidebar Navigation -->

    <!-- Content Section -->
    <div class="landing__main-content">
      <section id="chatbot" class="landing__main-section landing__chatbot">
        <app-landing-chatbot></app-landing-chatbot>
      </section>
      <section id="information" class="landing__main-section landing__information">
        <div class="landing__intro-wrapper">
          <div class="landing__intro-content">
            <!-- Dòng 1: CLS -->
            <p class="landing__intro-title">
              <span class="landing__intro-title-highlight">CLS</span>
            </p>

            <!-- Dòng 2: -->
            <p class="landing__intro-subtitle">
              Tra cứu, rà soát, truy vấn và hơn thế nữa
            </p>

            <!-- Mô tả -->
            <p class="landing__intro-description">
              Tối ưu thời gian trong rà soát các tài liệu pháp luật, và truy vấn
              mọi câu hỏi về pháp luật với Chatbot.
            </p>
            <a
              class="landing__intro-button"
              [routerLink]="['']"
              [attr.data-url]="fullLoginUrl"
            >
              Trải nghiệm ngay
            </a>
          </div>
          <figure class="landing__intro-illustration">
            <img
              src="assets/images/pages/landing/cls-preview.png"
              alt="cls preview"
              class="landing__intro-image"
              loading="lazy"
            />
          </figure>
        </div>
        <div class="landing__intro-metrics" #metricsSection>
          <div class="metric">
            <div class="metric-inner">
              <div class="metric-value" data-end="3000" data-suffix="+">0</div>
              <div class="metric-label">Người dùng</div>
            </div>
          </div>

          <div class="metric">
            <div class="metric-inner">
              <div class="metric-value" data-end="460000" data-suffix="+">0</div>
              <div class="metric-label">Văn bản pháp luật được cập nhật thường xuyên</div>
            </div>
          </div>

          <div class="metric">
            <div class="metric-inner">
              <div class="metric-value" data-end="10000" data-suffix="+">0</div>
              <div class="metric-label">Lượt tìm kiếm</div>
            </div>
          </div>

          <div class="metric">
            <div class="metric-inner">
              <div class="metric-value" data-end="5000" data-suffix="+">0</div>
              <div class="metric-label">Lượt truy vấn Chatbot</div>
            </div>
          </div>

          <div class="metric">
            <div class="metric-inner">
              <div class="metric-value" data-end="120000" data-suffix="+">0</div>
              <div class="metric-label">Lượt rà soát mẫu thuẫn</div>
            </div>
          </div>
        </div>
        <app-landing-workspace></app-landing-workspace>
        <app-landing-workspace-feature></app-landing-workspace-feature>
        <section id="articles" class="landing__main-section landing__articles">
          <div class="landing__articles-wrapper">
            <div class="landing__articles-header">
              <h2 class="landing__articles-title">Bài viết nổi bật</h2>
            </div>
            <app-landing-articles-carousel
              [articles]="articles"
              [itemsPerSlide]="4"
              [autoPlayIntervalMs]="10000"
            >
            </app-landing-articles-carousel>
          </div>
        </section>
        <!-- Legal Database Section -->
        <!-- <div class="landing__workspace-wrapper">
          <div class="landing__workspace-header">
            <h2 class="landing__workspace-title">Không gian làm việc</h2>
            <p class="landing__workspace-description">
              Với không gian làm việc có thể xử lý các văn bản tải lên và các
              văn bản từ cơ sở dữ liệu pháp luật cùng một lúc
            </p>
          </div>

          <div class="landing__workspace-cards">
            <app-landing-workspace-card
              [features]="workspaceFeatures"
            ></app-landing-workspace-card>
          </div>
        </div>
        <div class="landing__database-wrapper">
          <div class="landing__database-header">
            <h2 class="landing__database-title">Kho dữ liệu pháp luật</h2>
            <p class="landing__database-description">
              Truy cập kho dữ liệu pháp luật lớn với hơn 420.000 văn bản được
              cập nhật đầy đủ và chính thống. Hệ thống phân loại chi tiết, dễ
              dàng tra cứu mọi loại văn bản từ Luật, Nghị định cho đến Công văn,
              Quyết định… phục vụ nghiên cứu, tư vấn pháp lý và quản lý nhà
              nước.
            </p>
          </div>
          <div class="landing__database-stats">
            <div
              *ngFor="let stat of databaseStats"
              class="landing__database-stat-card"
              [class]="'landing__database-stat-card--' + stat.color"
            >
              <h3
                id="{{ stat.id }}"
                class="landing__database-stat-number"
                [attr.data-end]="stat.endValue"
                [attr.data-suffix]="stat.suffix"
              >
                0
              </h3>
              <p class="landing__database-stat-label">{{ stat.label }}</p>
            </div>
          </div>
        </div> -->
      </section>
      <app-landing-legal-data></app-landing-legal-data>
      <section id="mission" class="landing__main-section landing__mission">
        <div class="landing__mission-bg-overlay"></div>
        <div class="landing__mission-container">
          
          <div class="landing__mission-visual">
            <div class="network-anim-wrapper">
              <div class="dashed-orbit"></div>
              
              <div class="node-center">
                <span class="text-cls">CLS</span>
              </div>

              <div class="satellite-wrapper">
                
                <div class="satellite-item item-1">
                  <div class="icon-box">
                    <img src="assets/images/pages/landing/lawicon.svg" alt="Công lý" (error)="onIconError($event)"/>
                    <span class="material-fallback">⚖️</span>
                  </div>
                </div>
                
                <div class="satellite-item item-2">
                  <div class="icon-box">
                     <img src="assets/images/pages/landing/houseicon.svg" alt="Chính phủ" (error)="onIconError($event)"/>
                     <span class="material-fallback">🏛️</span>
                  </div>
                </div>

                <div class="satellite-item item-3">
                  <div class="icon-box">
                     <img src="assets/images/pages/landing/hmicon.svg" alt="Nhân sự" (error)="onIconError($event)"/>
                     <span class="material-fallback">👥</span>
                  </div>
                </div>

              </div>
            </div>
          </div>

          <div class="landing__mission-content">
            <h2 class="mission-title">Sứ mệnh</h2>
            
            <p class="mission-desc">
              Việt Nam đang đứng trước một vận hội lớn trong chuyển đổi số và chuyển đổi AI. 
              Là doanh nghiệp công nghệ thuần Việt, CMC xác định rõ sứ mệnh của mình không chỉ là phát triển kinh doanh, 
              mà quan trọng hơn là đóng góp thực chất vào năng lực tự chủ công nghệ quốc gia, 
              đồng hành cùng Chính phủ trong hiện thực hóa mục tiêu Nghị quyết 68 – để kinh tế tư nhân thực sự trở thành một động lực quan trọng của nền kinh tế.
            </p>

            <div class="mission-signature">
              <strong>Chủ tịch CMC Nguyễn Trung Chính</strong>
            </div>
          </div>

        </div>
      </section>
      <!-- <section id="articles" class="landing__main-section landing__articles">
        <div class="landing__articles-wrapper">
          <div class="landing__articles-header">
            <h2 class="landing__articles-title">Bài viết nổi bật</h2>
          </div>
          <app-landing-articles-carousel
            [articles]="articles"
            [itemsPerSlide]="4"
            [autoPlayIntervalMs]="10000"
          >
          </app-landing-articles-carousel>
        </div>
      </section> -->
      <!-- <section id="contact" class="landing__main-section landing__contact">
        <div class="landing__contact-wrapper">
          <div class="landing__contact-info">
            <h2 class="landing__contact-title">Liên hệ với chúng tôi</h2>
            <p class="landing__contact-description">
              Có thắc mắc hay câu hỏi vui lòng liên hệ với chúng tôi thông qua các cổng thông tin
            </p>
            <div class="landing__contact-list">
              <div class="landing__contact-item">
                <span class="landing__contact-icon"><i data-feather="phone"></i></span>
                <span class="landing__contact-text">0123.456.789</span>
              </div>
              <div class="landing__contact-item">
                <span class="landing__contact-icon"><i data-feather="mail"></i></span>
                <span class="landing__contact-text"><EMAIL></span>
              </div>
            </div>
          </div>

          <div class="landing__contact-form">
            <form class="landing__contact-form-el" (submit)="onContactSubmit($event)">
              <label class="landing__contact-label">Họ và tên
                <input class="landing__contact-input" type="text" name="fullName" placeholder="Nhập" required />
              </label>

              <label class="landing__contact-label">Email
                <input class="landing__contact-input" type="email" name="email" placeholder="Nhập" required />
              </label>

              <label class="landing__contact-label">Số điện thoại
                <input class="landing__contact-input" type="tel" name="phone" placeholder="Nhập" />
              </label>

              <label class="landing__contact-label">Lời nhắn
                <textarea class="landing__contact-textarea" name="message" placeholder="Nhập"></textarea>
              </label>

              <button type="submit" class="landing__contact-submit">Gửi lời nhắn</button>
            </form>
          </div>
        </div>
      </section> -->
    </div>
  </main>
  <button
    type="button"
    class="landing__to-top"
    *ngIf="showBackToTop"
    (click)="scrollToTop()"
    aria-label="Lên đầu trang"
    title="Lên đầu trang">
    <svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true">
      <path d="M12 5l-7 7h4v7h6v-7h4z" fill="currentColor"></path>
    </svg>
  </button>
  <app-landing-footer [url1]="urls?.url1"></app-landing-footer>

</div>
