<div class="document-table-wrapper">
  <div *ngIf="visible && !isTimKiemMode" class="document-table-container mt-1 p-1">
    <span class="mb-1 d-flex align-items-center justify-content-between">
      <b class="mb-0">{{ dataFile.ten_day_du || "" }}</b>
      <button
        type="button"
        class="close-btn"
        (click)="onClose()"
        aria-label="Đóng"
      >
        &times;
      </button>
    </span>
    <table class="document-table">
      <tbody>
        <tr *ngIf="isClauseNode">
          <td class="document-table__label">Nội dung điều</td>
          <td
            class="document-table__value document-table__value--clause"
            [attr.colspan]="3"
          >
            <div class="clause-content-wrapper">
              <ng-container *ngIf="isLoadingClauseContent">
                Đang hiển thị nội dung điều...
              </ng-container>
              <ng-container *ngIf="!isLoadingClauseContent && hasClauseContentError">
                Hiển thị nội dung điều lỗi
              </ng-container>
              <ng-container *ngIf="!isLoadingClauseContent && !hasClauseContentError">
                <div class="clause-content-body" [innerHTML]="getFormattedClauseContent()"></div>
              </ng-container>
            </div>
          </td>
        </tr>
        <tr>
          <td class="document-table__label">Số ký hiệu</td>
          <td class="document-table__value">{{ dataFile.so_hieu }}</td>
          <td class="document-table__label">Ngày ban hành</td>
          <td class="document-table__value">
            {{ dataFile.ngay_ban_hanh | safeDate }}
          </td>
        </tr>
        <tr>
          <td class="document-table__label">Loại văn bản</td>
          <td class="document-table__value">{{ dataFile.loai_van_ban }}</td>
          <td class="document-table__label">Ngày có hiệu lực</td>
          <td class="document-table__value">
            {{ dataFile.ngay_co_hieu_luc | safeDate }}
          </td>
        </tr>
        <tr>
          <td class="document-table__label">
            Cơ quan ban hành/ Chức danh/ Người ký
          </td>
          <td class="document-table__value">
            {{ dataFile.co_quan_ban_hanh || "Không có" }} /
            {{ dataFile?.chuc_danh || "Không có" }} /
            {{ dataFile.nguoi_ky || "Không có" }}
          </td>
          <td class="document-table__label">Phạm vi</td>
          <td class="document-table__value">Toàn quốc</td>
        </tr>
        <tr>
          <td class="document-table__label">Trích yếu</td>
          <td
            class="document-table__value"
            [attr.colspan]="3"
            [innerHTML]="dataFile?.trich_yeu"
          ></td>
        </tr>
        <tr *ngIf="typeDocument !== 'upload'">
          <td class="document-table__label">Tình trạng hiệu lực</td>
          <td
            class="document-table__value document-status"
            [attr.colspan]="3"
            [ngClass]="{
              'document-status--active':
                dataFile.tinh_trang_hieu_luc === 'Còn hiệu lực',
              'document-status--warning':
                dataFile.tinh_trang_hieu_luc === 'Hết hiệu lực một phần',
              'document-status--danger':
                dataFile.tinh_trang_hieu_luc === 'Hết hiệu lực toàn bộ',
              'document-status--info': ![
                'Còn hiệu lực',
                'Hết hiệu lực một phần',
                'Hết hiệu lực toàn bộ'
              ].includes(dataFile.tinh_trang_hieu_luc)
            }"
          >
            {{ dataFile.tinh_trang_hieu_luc }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

