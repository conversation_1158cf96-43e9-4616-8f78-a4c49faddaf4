import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CmsComponent } from './cms.component';
import { RouterModule, Routes } from "@angular/router";
import { BreadcrumbModule } from "app/layout/components/content-header/breadcrumb/breadcrumb.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { ListWorkComponent } from './list-work/list-work.component';
import { ListPostComponent } from './list-post/list-post.component';
import { ListQuestionComponent } from './list-question/list-question.component';
import { ListLegalDocumentComponent } from './list-legal-document/list-legal-document.component';
import { NgSelectModule } from "@ng-select/ng-select";
import { CoreCommonModule } from "@core/common.module";
import { DragulaModule } from 'ng2-dragula';
import { NgxDatatableModule } from "@swimlane/ngx-datatable";
import { CmsDashboardComponent } from './cms-dashboard/cms-dashboard.component';
import { NgApexchartsModule } from 'ng-apexcharts';
import { QuillModule } from 'ngx-quill';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CmsRoleComponent } from './cms-role/cms-role.component';
import { Ng2FlatpickrModule } from 'ng2-flatpickr';

const routes: Routes = [
  {
    path: '',
    component: CmsComponent,
    children: [
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
      { path: 'dashboard', component: CmsDashboardComponent },
      { path: 'list-work', component: ListWorkComponent },
      { path: 'list-post', component: ListPostComponent },
      { path: 'list-question', component: ListQuestionComponent },
      { path: 'list-legal-document', component: ListLegalDocumentComponent },
    ]
  }
];

@NgModule({
  declarations: [
    CmsComponent,
    ListWorkComponent,
    ListPostComponent,
    ListQuestionComponent,
    ListLegalDocumentComponent,
    CmsDashboardComponent,
    CmsRoleComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    BreadcrumbModule,
    NgbModule,
    NgSelectModule,
    CoreCommonModule,
    DragulaModule.forRoot(),
    NgxDatatableModule,
    NgApexchartsModule,
    QuillModule.forRoot(),
    FormsModule,
    ReactiveFormsModule,
    Ng2FlatpickrModule,
  ]
})
export class CmsModule { }
