import { Component, Input, OnInit } from '@angular/core';
import { SharedConversationService } from '../../shared-conversation.service';
import { finalize } from "rxjs/operators";
import { ToastrService } from "ngx-toastr";

@Component({
  selector: 'app-shared-conversation-content',
  templateUrl: './shared-conversation-content.component.html',
  styleUrls: ['./shared-conversation-content.component.scss']
})
export class SharedConversationContentComponent implements OnInit {
  @Input() shareId: string;

  conversationData: any;
  messages: any[] = [];
  showScrollButton = false;
  isFetching = true;
  chatHeight: number = 0;
  public isSpeechToText: boolean = false;
  public idMessageSpeechToText: string = null;
  private audio: HTMLAudioElement | null = null;
  isPaused = false;

  constructor(
    private _sharedConversationService: SharedConversationService,
    private _toastService: ToastrService
  ) {}

  ngOnInit(): void {
    this._sharedConversationService
      .getsharedConversationData(this.shareId)
      .pipe( finalize(() => this.isFetching = false) )
      .subscribe({
        next: (data) => {
          this.conversationData = data;
          this.messages = data.messages || [];
        },
        error: (error) => {
          // window.location.href = '/pages/miscellaneous/error';
        }
      });
  }

  onAnswerClick(event: any): void {
    // Do chưa có thiết kế nên tạm thời không xử lý sự kiện này
    event.preventDefault();
  }

  formatContent(text: string): string {
    return text
      .replace(/\[([^\]]+)\]\((legal:[^)]+)\)/g, `<a href="$2" class="legal-link">$1</a>`)
      .replace("<answer>", "")
      .replace("</answer>", "");
  }

  textToSpeech(message) {
    if ("speechSynthesis" in window) {
      // Dừng tất cả speech đang chạy
      speechSynthesis.cancel();

      this.idMessageSpeechToText = message.id;
      this.isSpeechToText = true;
      this.isPaused = false;

      // Tạo utterance mới
      const utterance = new SpeechSynthesisUtterance(message.text || message.answer);

      // Cấu hình voice
      utterance.lang = "vi-VN"; // Tiếng Việt
      utterance.rate = 1; // Tốc độ đọc
      utterance.pitch = 1; // Cao độ giọng
      utterance.volume = 1; // Âm lượng

      // Xử lý sự kiện kết thúc
      utterance.onend = () => {
        this.isSpeechToText = false;
        this.isPaused = false;
        this.idMessageSpeechToText = null;
      };

      utterance.onerror = (err) => {
        console.error("Lỗi khi phát speech:", err);
        this.isSpeechToText = false;
        this.isPaused = false;
        this.idMessageSpeechToText = null;
      };

      // Bắt đầu đọc
      speechSynthesis.speak(utterance);
    } else {
      this._toastService.error("Trình duyệt không hỗ trợ text-to-speech", "Lỗi", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }

  pauseTextToSpeech() {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      this.isPaused = true;
      // console.log("Đã tạm dừng speech!");
    }
  }

  resumeTextToSpeech() {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      this.isPaused = false;
      // console.log("Đã tiếp tục speech!");
    }
  }

  endTextToSpeech() {
    speechSynthesis.cancel();
    this.isSpeechToText = false;
    this.isPaused = false;
    this.idMessageSpeechToText = null;
  }

}
