import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { Observable } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class CommercialSearchService {

  constructor(
    private _httpClient: HttpClient
  ) { }

}