.document-table-wrapper {
  position: absolute;
  bottom: 0;
  padding: 10px;
  width: 100%;
}

.document-table-container {
  height: auto;
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;

  .close-btn {
    appearance: none;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 24px;
    line-height: 1;
    cursor: pointer;
    padding: 2px 8px;
    border-radius: 4px;

    &:hover {
      background: rgba(0, 0, 0, 0.06);
      color: #343a40;
    }
  }
}

.document-table {
  width: 100%;
  border-collapse: collapse;

  .document-table__label {
    background-color: white;
    padding: 8px 12px;
    font-weight: 600;
    border: 1px solid #ddd;
    width: 20%;

    &:first-child {
      border-left: none;
    }
  }

  .document-table__value {
    background-color: white;
    padding: 8px 12px;
    border: 1px solid #ddd;

    // Special styling for clause content cell
    &--clause {
      max-height: 300px;
      overflow-y: auto;
    }
  }

  tr {
    &:first-child {

      .document-table__label,
      .document-table__value {
        border-top: none;
      }
    }

    &:last-child {

      .document-table__label,
      .document-table__value {
        border-bottom: none;
      }
    }
  }

  .clause-content-wrapper {
    max-height: 100px;
    overflow-y: auto;
    padding: 8px 0;
  }

  .clause-content-body {
    line-height: 1.5;
    color: #212529;
  }

  .document-status {
    font-weight: 500;

    &--active {
      color: #28a745;
    }

    &--warning {
      color: #ffc107;
    }

    &--danger {
      color: #dc3545;
    }

    &--info {
      color: #17a2b8;
    }
  }
}