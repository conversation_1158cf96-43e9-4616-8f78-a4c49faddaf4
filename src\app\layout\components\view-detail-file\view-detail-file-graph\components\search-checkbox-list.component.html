<div>
  <!-- Search bar -->
  <div class="form-group mb-1">
    <div class="input-group input-group-merge">
      <div class="input-group-prepend">
        <span class="input-group-text">
          <i data-feather="search" class="text-muted"></i>
        </span>
      </div>
      <input
        type="text"
        class="form-control"
        [placeholder]="searchPlaceholder"
        aria-label="Tìm kiếm bộ lọc"
        [(ngModel)]="searchTerm"
        (ngModelChange)="onSearchChange()"
        [ngModelOptions]="{ standalone: true }"
      />
    </div>
    <!-- Select All and Clear buttons -->
    <div class="d-flex justify-content-start mt-1">
      <a href="javascript:void(0)" class="text-primary mr-2" (click)="selectAll()">Chọn tất cả</a>
      <a href="javascript:void(0)" class="text-primary" (click)="clearAll()">Bỏ chọn</a>
    </div>
  </div>
  
  <!-- Checkbox list -->
  <div class="checkbox-list" [style.max-height.px]="maxHeight" [style.overflow-y]="maxHeight ? 'auto' : 'visible'" [style.overflow-x]="maxHeight ? 'hidden' : 'visible'">
    <div 
      class="custom-control custom-checkbox mb-50"
      *ngFor="let option of filteredOptions"
    >
      <input
        type="checkbox"
        class="custom-control-input"
        [id]="idPrefix + '-' + option.value"
        [checked]="isSelected(option.value)"
        (change)="toggleSelection(option.value)"
      />
      <label 
        class="custom-control-label" 
        [for]="idPrefix + '-' + option.value"
      >
        <span 
          class="color-dot" 
          *ngIf="getColor(option)"
          [style.background]="getColor(option)"
        ></span>
        {{ option.label }}
      </label>
    </div>
    
    <!-- No results fallback -->
    <div 
      *ngIf="filteredOptions.length === 0"
      class="text-muted text-center p-1"
    >
      Không có kết quả tìm kiếm
    </div>
  </div>
</div>

