import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { CoreCommonModule } from "@core/common.module";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { WorkSpaceControlComponent } from "./work-space-control.component";

@NgModule({
  declarations: [WorkSpaceControlComponent],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CoreCommonModule,
    NgbModule,
  ],
  exports: [WorkSpaceControlComponent],
})
export class WorkSpaceControlModule { }
