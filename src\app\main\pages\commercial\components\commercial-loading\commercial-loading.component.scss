.clsc-loading-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &.clsc-loading-overlay {
    background-color: rgba(0, 0, 0, 0.2);
  }

  &.clsc-loading-fullscreen {
    position: fixed;
    inset: 0;
    z-index: 9999;
  }
}

// .clsc-loading-spinner {
//   .spinner-border {
//     width: 2.8rem;
//     height: 2.8rem;
//     border-width: 0.28rem;
//     color: #007bff;
//   }
// }

.clsc-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

// .clsc-loading-icon img {
//   width: 64px;
//   height: 64px;
// }

.clsc-loading-text {
  // font-size: 14px;
  // color: #ffffff;
  text-align: center;
}

@keyframes clsc-loading-pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.clsc-loading-pulse {
  animation: clsc-loading-pulse 3s ease-in-out infinite;
}
