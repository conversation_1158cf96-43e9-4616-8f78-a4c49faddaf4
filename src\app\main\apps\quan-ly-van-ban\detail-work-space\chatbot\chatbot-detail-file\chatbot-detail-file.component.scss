:host {
  display: block;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
}

.document-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100%;
  height: 60px;
  padding: 0 1.5rem;
  border-bottom: 1px solid #ebe9f1;
  background-color: #fff;
  flex-shrink: 0;
  > div:first-child {
    display: flex;
    align-items: center;
    overflow: hidden;
    flex: 1; 
    min-width: 0;
    margin-right: 1rem;
  }

  .header-actions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    margin-left: auto;
  }
}

.chatbot-file-viewer {
  border-left: 1px solid #e0e0e0; 
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.05);
  
  .viewer-header {
    background-color: #f8f9fa;
    height: 56px;
    flex-shrink: 0;
  }

  .viewer-content {
    /* Style cho nội dung văn bản cho dễ đọc */
    .document-body {
      font-family: 'Times New Roman', serif;
      font-size: 16px;
      line-height: 1.6;
      color: #333;
      
      p { margin-bottom: 1rem; }
      h1, h2, h3 { margin-top: 1.5rem; margin-bottom: 1rem; font-weight: bold; }
      ul, ol { padding-left: 2rem; margin-bottom: 1rem; }
    }
  }
}
/* Style cho nút Lưu (Lấy từ code mẫu) */
.view-detail-file-save-button {
  min-width: 70px;  
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-saving {
  max-height: 29.6px;
}

.font-sm {
  font-size: 0.875rem !important; 
}

.round {
  border-radius: 1.5rem !important;
}

.mr-1 {
  margin-right: 0.5rem !important;
}

.btn-action-close {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
  
  &:hover {
    background-color: rgba(0,0,0,0.05);
  }
  
  i {
    width: 18px;
    height: 18px;
  }
}
@media (max-width: 991.98px) {
  .chatbot-file-viewer {
    border-left: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100vh !important;
    z-index: 1050;
  }
}
app-chatbot-detail-file {
  .viewer-content.p-4 {
    padding: 1rem 2rem 2rem 2rem !important;
  }
  .document-body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: #333;
    line-height: 1.6;
    font-size: 14px;

    h1, h2, h3, h4, h5, h6 {
      font-weight: 600;
      color: #2c3e50;
      margin-top: 1.5em;
      margin-bottom: 0.75em;
    }

    h1 { font-size: 1.5rem; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }
    h2 { font-size: 1.25rem; }
    h3 { font-size: 1.1rem; }

    /* Style cho đoạn văn */
    p {
      margin-bottom: 1em;
      text-align: justify;
    }

    /* Style cho danh sách (* item) */
    ul, ol {
      padding-left: 2em;
      margin-bottom: 1em;

      li {
        margin-bottom: 0.5em;
      }
    }

    strong {
      font-weight: 700;
      color: #000;
    }

    /* Style cho Link văn bản pháp luật */
    a {
      color: #008fd3;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }

      &.legal-link {
        // color: #0056b3;
        color: #008fe3;
        font-weight: 500;
        // background-color: rgba(0, 86, 179, 0.05);
        padding: 0 4px;
        border-radius: 4px;
      }
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1em 0;
      
      th, td {
        border: 1px solid #ddd;
        padding: 8px;
      }
      th {
        background-color: #f2f2f2;
        font-weight: bold;
      }
    }
  }
}