import { Injectable } from '@angular/core';
import * as d3 from 'd3';
import {
  GraphNode,
  GraphLink,
  ApiNode,
} from '../types/graph.types';
import {
  D3GraphNode,
  D3GraphLink,
  GraphRendererCallbacks,
} from '../models/graph-renderer.models';
import {
  NODE_COLORS,
  NODE_SIZES,
  CHART_CONFIG,
} from '../constants/graph.constants';
import { mapStrengthToColor } from '../helper/helper';

/**
 * Service for rendering graph using D3.js
 */
@Injectable({
  providedIn: 'root',
})
export class GraphRendererService {
  private readonly LINK_ARROW_LENGTH = 10;
  private readonly LINK_ARROW_PADDING = 6;
  private readonly DRAG_SMOOTHING_ALPHA = 0.2;

  private d3Svg?: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private d3RootGroup?: d3.Selection<SVGGElement, unknown, null, undefined>;
  private d3Simulation?: d3.Simulation<D3GraphNode, D3GraphLink>;
  private previousNodePositions: Map<string, { x: number; y: number }> = new Map();
  private simulationStopTimeout?: number;
  private convergenceCheckInterval?: number;
  private tickCounter: number = 0;
  private readonly TICK_THROTTLE: number = 3; // Update DOM every 3 ticks
  private nodeSelection?: d3.Selection<SVGGElement, D3GraphNode, SVGGElement, unknown>;
  private linkSelection?: d3.Selection<SVGGElement, D3GraphLink, SVGGElement, unknown>;

  /**
   * Get current node positions from simulation before destroying
   */
  getCurrentNodePositions(): Map<string, { x: number; y: number }> {
    const positions = new Map<string, { x: number; y: number }>();
    if (this.d3Simulation) {
      const nodes = this.d3Simulation.nodes();
      nodes.forEach(node => {
        if (node.x != null && node.y != null) {
          positions.set(node.id, { x: node.x, y: node.y });
        }
      });
    }
    return positions;
  }

  /**
   * Render graph in container
   */
  renderGraph(
    container: HTMLElement,
    nodes: GraphNode[],
    links: GraphLink[],
    apiNodeMap: Map<string, ApiNode>,
    rootNodeId: string,
    d3Nodes: D3GraphNode[],
    d3Links: D3GraphLink[],
    callbacks?: GraphRendererCallbacks,
    isTimKiemMode: boolean = false
  ): void {
    if (!nodes.length) {
      this.destroy();
      container.innerHTML = '';
      return;
    }

    // Save current node positions before updating
    this.previousNodePositions = this.getCurrentNodePositions();

    const { width, height } = this.getContainerSize(container);

    // Check if SVG already exists - if yes, just update data without destroying
    let svg = this.d3Svg;
    let rootGroup = this.d3RootGroup;

    if (!svg || !rootGroup) {
      // First render: create SVG structure
      this.destroy();
      container.innerHTML = '';

      svg = d3
        .select(container)
        .append('svg')
        .attr('class', 'd3-svg')
        .attr('width', '100%')
        .attr('height', '100%')
        .attr('viewBox', `0 0 ${width} ${height}`)
        .attr('preserveAspectRatio', 'xMidYMid meet');

      this.d3Svg = svg;

      // Create arrow marker
      const defs = svg.append('defs');
      defs
        .append('marker')
        .attr('id', 'graph-link-arrow')
        .attr('markerUnits', 'userSpaceOnUse')
        .attr('viewBox', '0 -5 10 10')
        .attr('refX', this.LINK_ARROW_LENGTH / 2)
        .attr('refY', 0)
        .attr('markerWidth', this.LINK_ARROW_LENGTH)
        .attr('markerHeight', this.LINK_ARROW_LENGTH)
        .attr('orient', 'auto')
        .append('path')
        .attr('d', 'M0,-5L10,0L0,5Z')
        .attr('fill', '#666')
        .attr('stroke', 'none');

      rootGroup = svg.append('g').attr('class', 'd3-graph-root');
      this.d3RootGroup = rootGroup;
    } else {
      // Update existing SVG viewBox if container size changed
      svg.attr('viewBox', `0 0 ${width} ${height}`);
    }

    // Get or create link group
    let linkGroup = rootGroup.select<SVGGElement>('g.graph-links');
    if (linkGroup.empty()) {
      linkGroup = rootGroup.append('g').attr('class', 'graph-links');
    }
    this.linkSelection = linkGroup
      .selectAll<SVGGElement, D3GraphLink>('g')
      .data(d3Links, (d: any) => d.id)
      .join(
        (enter) => {
          const group = enter.append('g').attr('class', 'relationship');
          group
            .append('path')
            .attr('class', 'overlay')
            .attr('fill', 'none');
          group
            .append('path')
            .attr('class', 'outline')
            .attr('fill', 'none')
            .attr('marker-end', 'url(#graph-link-arrow)');
          return group;
        },
        (update) => update,
        (exit) => exit.remove()
      );

    // Update link visual properties (for both new and existing links)
    this.linkSelection.select('path.outline')
      .attr('stroke', (d) =>
        d.__isBaiBo ? NODE_COLORS.ARTICLE : mapStrengthToColor(d.strength)
      )
      .attr('stroke-width', CHART_CONFIG.LINE_STYLE.WIDTH)
      .attr('stroke-dasharray', (d) => (d.__isBaiBo ? '4 2' : null))
      .attr('stroke-opacity', (d) => d.opacity)
      .attr('opacity', (d) => d.opacity);

    // Get or create link label group
    let linkLabelGroup = rootGroup.select<SVGGElement>('g.graph-link-labels');
    if (linkLabelGroup.empty()) {
      linkLabelGroup = rootGroup.append('g').attr('class', 'graph-link-labels');
    }
    const linkLabels = linkLabelGroup
      .selectAll<SVGGElement, D3GraphLink>('g')
      .data(
        d3Links.filter((link) => !!link.label),
        (d: any) => d.id
      )
      .join(
        (enter) => {
          const group = enter.append('g').attr('class', 'graph-link-label');
          group
            .append('rect')
            .attr('class', 'text-bg')
            .attr('fill', '#ffffff')
            .attr('fill-opacity', 1)
            .attr('rx', 2)
            .attr('ry', 2);
          group
            .append('text')
            .attr('class', 'text')
            .attr('fill', '#000000')
            .attr('font-size', '8px')
            .attr('pointer-events', 'none')
            .attr('text-anchor', 'middle')
            .attr('dy', '0.35em')
            .text((d) => d.label || '');
          return group;
        },
        (update) => {
          // Update label text and ensure background rect is adjusted later
          update.select('text.text').text((d) => d.label || '');
          return update;
        },
        (exit) => exit.remove()
      )
      .attr('opacity', (d) => d.opacity ?? 1);

    // Setup zoom (only on first render, after linkLabels exist)
    if (!svg.select('g.d3-graph-root').property('__zoomInitialized')) {
      const LABEL_MIN_PIXEL_RADIUS = 25; // Hide labels when screen radius < 25px

      const zoomBehavior = d3
        .zoom<SVGSVGElement, unknown>()
        .scaleExtent([0.2, 5])
        .on('zoom', (event) => {
          rootGroup.attr('transform', event.transform.toString());
          const scale = event.transform.k;

          // Update label visibility based on screen size (nodes always visible)
          svg.selectAll<SVGGElement, D3GraphNode>('g.graph-node').each(function(d) {
            const nodeRadius = (d.symbolSize || NODE_SIZES.REGULAR) / 2;
            const screenRadius = nodeRadius * scale;
            
            // Hide/show label only
            const labelVisible = screenRadius >= LABEL_MIN_PIXEL_RADIUS;
            d3.select(this).select('.graph-node-label')
              .style('display', labelVisible ? 'block' : 'none');
          });

          // Link labels visibility
          linkLabels.attr("display", scale > 0.5 ? "block" : "none");
        });
      svg.call(zoomBehavior as any);
      rootGroup.property('__zoomInitialized', true);
    }

    // Get or create node group
    let nodeGroup = rootGroup.select<SVGGElement>('g.graph-nodes');
    if (nodeGroup.empty()) {
      nodeGroup = rootGroup.append('g').attr('class', 'graph-nodes');
    }

    const dragBehaviour = d3
      .drag<SVGGElement, D3GraphNode>()
      .on('start', (event, d) => {
        if (!event.active && this.d3Simulation) {
          this.d3Simulation.alphaTarget(0.3).restart();
        }
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d) => {
        const currentFx = d.fx ?? d.x ?? event.x;
        const currentFy = d.fy ?? d.y ?? event.y;
        d.fx = currentFx + (event.x - currentFx) * this.DRAG_SMOOTHING_ALPHA;
        d.fy = currentFy + (event.y - currentFy) * this.DRAG_SMOOTHING_ALPHA;
        if (callbacks?.onNodeDrag) {
          callbacks.onNodeDrag(d.id, d.fx, d.fy);
        }
      })
      .on('end', (event) => {
        if (!event.active && this.d3Simulation) {
          this.d3Simulation.alphaTarget(0);
        }
      });

    this.nodeSelection = nodeGroup
      .selectAll<SVGGElement, D3GraphNode>('g')
      .data(d3Nodes, (d: any) => d.id)
      .join(
        (enter) => {
          const group = enter.append('g');
          
          // Add ring layer
          group.append('circle')
            .attr('class', 'ring')
            .attr('r', (d) => {
              const nodeRadius = (d.symbolSize || NODE_SIZES.REGULAR) / 2;
              return nodeRadius * 1.16;
            });

          // Add outline circle
          group.append('circle')
            .attr('class', 'outline')
            .attr('r', (d) => (d.symbolSize || NODE_SIZES.REGULAR) / 2)
            .attr('fill', (d) => d.color || NODE_COLORS.DEFAULT)
            .attr('stroke', '#fff')
            .attr('stroke-width', 1.5);

          // Add label
          group.append('text')
            .attr('class', 'graph-node-label')
            .attr('text-anchor', 'middle')
            .attr('fill', '#fff')
            // Scale font size proportionally to node radius
            // Base: REGULAR node (symbolSize=80) uses font-size 10
            // => fontSize = 10 * (symbolSize / 80)
            .attr('font-size', (d) => {
              const symbolSize = d.symbolSize || NODE_SIZES.REGULAR;
              const scale = symbolSize / NODE_SIZES.REGULAR;
              return 10 * scale;
            })
            .attr('pointer-events', 'all');

          // Add tooltip
          group.append('title');

          return group;
        },
        (update: d3.Selection<SVGGElement, D3GraphNode, SVGGElement, unknown>) => {
          // Update ring radius based on latest symbolSize
          update
            .select('circle.ring')
            .attr('r', (d) => {
              const nodeRadius = (d.symbolSize || NODE_SIZES.REGULAR) / 2;
              return nodeRadius * 1.16;
            });

          // Update outline radius and color
          update
            .select('circle.outline')
            .attr('r', (d) => (d.symbolSize || NODE_SIZES.REGULAR) / 2)
            .attr('fill', (d) => d.color || NODE_COLORS.DEFAULT);

          // Update font size to match node size changes
          update
            .select('text.graph-node-label')
            .attr('font-size', (d) => {
              const symbolSize = d.symbolSize || NODE_SIZES.REGULAR;
              const scale = symbolSize / NODE_SIZES.REGULAR;
              return 10 * scale;
            });

          return update;
        },
        (exit) => exit.remove()
      )
      .attr('class', (d) =>
        `graph-node${d.isBlurred ? ' node-blurred' : ''}`
      )
      .style('cursor', (d) => (d.isBlurred ? 'not-allowed' : 'pointer'))
      .on('click', (event, d) => {
        if (d.isBlurred) {
          return;
        }
        const apiNode = apiNodeMap.get(d.id) ?? null;
        if (apiNode && callbacks?.onNodeClick) {
          callbacks.onNodeClick(d.id, apiNode);
        }
      })
      .on('contextmenu', (event, d) => {
        if (d.isBlurred) {
          return;
        }
        event.preventDefault();
        const apiNode = apiNodeMap.get(d.id) ?? null;
        if (apiNode && callbacks?.onNodeRightClick) {
          callbacks.onNodeRightClick(event as MouseEvent, d.id, apiNode);
        }
      })
      .on('dblclick', (event, d) => {
        if (d.isBlurred) {
          return;
        }
        event.preventDefault();
        event.stopPropagation();
        const apiNode = apiNodeMap.get(d.id) ?? null;
        if (apiNode && callbacks?.onNodeDoubleClick) {
          callbacks.onNodeDoubleClick(d.id, apiNode);
        }
      })
      .call(dragBehaviour);

    // Update existing nodes' visual properties
    this.nodeSelection.select('circle.outline')
      .attr('fill', (d) => d.color || NODE_COLORS.DEFAULT)
      .attr('fill-opacity', (d) => d.opacity ?? 1)
      .classed('graph-node--blurred', (d) => !!d.isBlurred);

    // Update labels
    const labelSelection = this.nodeSelection.select('text.graph-node-label');
    labelSelection.each((d, i, nodes) => {
      const textEl = d3.select(nodes[i]);
      this.renderMultilineLabel(textEl, d.label, d);
    });

    // Update tooltips
    this.nodeSelection.select('title').text((d) => {
      const apiNode = apiNodeMap.get(d.id) ?? null;
      if (!apiNode || d.isBlurred) {
        return '';
      }
      if (callbacks?.formatTooltip) {
        return callbacks.formatTooltip(d.id, apiNode, d.label);
      }
      return d.label;
    });

    // Apply initial label visibility based on current zoom level (nodes always visible)
    const currentTransform = d3.zoomTransform(svg.node() as Element);
    const currentScale = currentTransform ? currentTransform.k : 1;
    const LABEL_MIN_PIXEL_RADIUS = 25;

    this.nodeSelection.each(function(d) {
      const nodeRadius = (d.symbolSize || NODE_SIZES.REGULAR) / 2;
      const screenRadius = nodeRadius * currentScale;
      
      // Hide/show label only
      const labelVisible = screenRadius >= LABEL_MIN_PIXEL_RADIUS;
      d3.select(this).select('.graph-node-label')
        .style('display', labelVisible ? 'block' : 'none');
    });

    // Ensure root node has a position first (centered)
    const rootNode = d3Nodes.find(n => n.id === rootNodeId);
    if (rootNode && (rootNode.x == null || rootNode.y == null)) {
      rootNode.x = width / 2;
      rootNode.y = height / 2;
    }

    // Initialize new nodes near their parent nodes before running simulation
    // This prevents edges from stretching too far when new nodes are added
    // First pass: find all new nodes (nodes without positions)
    const newNodes = new Set<D3GraphNode>();
    d3Nodes.forEach(node => {
      if (node.x == null || node.y == null) {
        newNodes.add(node);
      }
    });

    // Calculate initial cluster radius based on number of nodes
    // For large graphs, start with a tighter cluster
    const nodeCount = d3Nodes.length;
    const initialClusterRadius = Math.min(150, Math.max(80, 2000 / nodeCount));

    // Second pass: initialize new nodes near their first parent that has a position
    // If no parent, cluster them around the root node
    const newNodesArray = Array.from(newNodes); // Convert Set to Array for index access
    newNodesArray.forEach((newNode, index) => {
      // Find the first link where this node is the target and source has a position
      const parentLink = d3Links.find(link => {
        const t = link.target as D3GraphNode;
        const s = link.source as D3GraphNode;
        return t && t.id === newNode.id && s && s.x != null && s.y != null;
      });

      if (parentLink) {
        const s = parentLink.source as D3GraphNode;
        // Initialize near parent with small random offset (tighter cluster)
        const angle = (index / newNodesArray.length) * Math.PI * 2; // Distribute in circle
        const radius = initialClusterRadius * 0.3; // Tighter initial cluster
        newNode.x = s.x! + Math.cos(angle) * radius + (Math.random() * 20 - 10);
        newNode.y = s.y! + Math.sin(angle) * radius + (Math.random() * 20 - 10);
      } else {
        // No parent with position found, cluster around root or center
        const centerX = rootNode?.x ?? width / 2;
        const centerY = rootNode?.y ?? height / 2;
        // Distribute nodes in a circle around center, but keep them close initially
        const angle = (index / newNodesArray.length) * Math.PI * 2;
        const radius = initialClusterRadius * 0.5; // Start closer to center
        newNode.x = centerX + Math.cos(angle) * radius;
        newNode.y = centerY + Math.sin(angle) * radius;
      }
    });

    // Setup force simulation with optimized settings
    // Reset tick counter for new simulation
    this.tickCounter = 0;
    
    // Clear any existing timeout and interval
    if (this.simulationStopTimeout) {
      clearTimeout(this.simulationStopTimeout);
      this.simulationStopTimeout = undefined;
    }
    if (this.convergenceCheckInterval) {
      clearInterval(this.convergenceCheckInterval);
      this.convergenceCheckInterval = undefined;
    }

    const baseEdgeLength = CHART_CONFIG.FORCE.EDGE_LENGTH;
    const computeEdgeLength = (link: D3GraphLink) => {
      const source = link.source as D3GraphNode;
      const target = link.target as D3GraphNode;
      const sourceSize = source?.symbolSize || NODE_SIZES.REGULAR;
      const targetSize = target?.symbolSize || NODE_SIZES.REGULAR;
      // Extend edge length when connecting bigger nodes so labels don't sit inside nodes
      const maxNodeSize = Math.max(sourceSize, targetSize);
      const extra = maxNodeSize * 0.6; // tuned factor
      return baseEdgeLength + extra;
    };

    const simulation = d3
      .forceSimulation<D3GraphNode>(d3Nodes)
      .force(
        'link',
        d3
          .forceLink<D3GraphNode, D3GraphLink>(d3Links)
          .id((d: any) => d.id)
          .distance((d) => computeEdgeLength(d))
      )
      .force('charge', d3.forceManyBody().strength(-CHART_CONFIG.FORCE.REPULSION))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force(
        'collision',
        d3.forceCollide().radius(
          (d) => (d.symbolSize || NODE_SIZES.REGULAR) / 2 + 5
        )
      )
      // Optimize simulation: balanced convergence
      // Use less aggressive alphaDecay to allow more time for nodes to settle
      .alphaDecay(0.04) // Moderate decay - allows more time for convergence (default is 0.0224)
      .velocityDecay(0.6) // Standard velocity decay for natural movement
      .on('tick', () => {
        // Throttle DOM updates: only update every N ticks for better performance
        this.tickCounter++;
        const shouldUpdate = this.tickCounter % this.TICK_THROTTLE === 0;
        
        // Always update on last tick or when throttled update is due
        // Also update when simulation is naturally ending (alpha very low)
        if (shouldUpdate || simulation.alpha() < 0.005) {
          // Use requestAnimationFrame for smooth rendering
          requestAnimationFrame(() => {
            // Update link paths
            if (this.linkSelection) {
              this.linkSelection.each((d, i, nodes) => {
              const group = d3.select(nodes[i]);
              const pathData = this.buildLinkPath(d);
              group
                .select<SVGPathElement>('path.outline')
                .attr('d', pathData)
                .attr('opacity', (d) => d.opacity ?? 1);
              group.select<SVGPathElement>('path.overlay').attr('d', pathData);
              });
            }

            // Update link labels
            linkLabels.attr('transform', (d) => this.getLinkLabelTransform(d));
            linkLabels.attr('opacity', (d) => d.opacity ?? 1);
            linkLabels.each((d, i, nodes) => {
              const group = d3.select(nodes[i]);
              const textEl = group.select<SVGTextElement>('text.text');
              const rectEl = group.select<SVGRectElement>('rect.text-bg');
              const textNode = textEl.node();
              if (!textNode) {
                return;
              }
              const bbox = textNode.getBBox();
              const padding = 3;
              const labelWidth = bbox.width + padding * 2;
              const labelHeight = bbox.height + padding * 2;
              rectEl
                .attr('width', labelWidth)
                .attr('height', labelHeight)
                .attr('x', -labelWidth / 2)
                .attr('y', -labelHeight / 2);
            });

            // Update node positions
            if (this.nodeSelection) {
              this.nodeSelection.attr('transform', (d) => {
              const x = d.x ?? width / 2;
              const y = d.y ?? height / 2;
              return `translate(${x}, ${y})`;
              });
            }
          });
        }
      });

    this.d3Simulation = simulation;

    // Stop simulation after sufficient time for convergence
    // Increased duration to allow nodes to settle into a good layout
    // For large graphs (2000+ nodes), allow more time
    // Reuse nodeCount already declared above (line 338)
    const simulationDuration = nodeCount > 1000 ? 3000 : 2500; // 2.5-3 seconds
    
    // Also check for natural convergence (simulation alpha drops low)
    let hasConverged = false;
    const checkConvergence = () => {
      if (simulation && simulation.alpha() < 0.01 && !hasConverged) {
        hasConverged = true;
        // Give it a few more ticks to settle, then stop
        setTimeout(() => {
          if (simulation) {
            simulation.stop();
          }
        }, 200);
      }
    };

    // Check convergence periodically
    this.convergenceCheckInterval = window.setInterval(() => {
      checkConvergence();
      if (hasConverged && this.convergenceCheckInterval) {
        clearInterval(this.convergenceCheckInterval);
        this.convergenceCheckInterval = undefined;
      }
    }, 100);

    // Stop simulation after maximum duration to prevent indefinite running
    // This ensures the simulation doesn't consume CPU indefinitely
    this.simulationStopTimeout = window.setTimeout(() => {
      if (this.convergenceCheckInterval) {
        clearInterval(this.convergenceCheckInterval);
        this.convergenceCheckInterval = undefined;
      }
      if (simulation && !hasConverged) {
        simulation.stop();
      }
    }, simulationDuration);
  }

  private renderMultilineLabel(
    textSelection: d3.Selection<SVGTextElement, D3GraphNode, null, undefined>,
    label: string,
    nodeData: D3GraphNode
  ): void {
    if (!label) {
      textSelection.selectAll('tspan').remove();
      return;
    }

    // Calculate available width based on node radius (with padding)
    const symbolSize = nodeData.symbolSize || NODE_SIZES.REGULAR;
    const nodeRadius = symbolSize / 2;
    const padding = 8; // Padding from node edge
    const maxWidth = (nodeRadius - padding) * 2; // Diameter minus padding on both sides

    // Get font size for text measurement
    const fontSize = parseFloat(textSelection.attr('font-size')) || 10;
    const lineHeight = fontSize * 1.2; // Line height based on font size

    // Create a temporary text element for measuring text width
    const tempText = textSelection.append('tspan').style('visibility', 'hidden');
    const measureText = (text: string): number => {
      tempText.text(text);
      const bbox = (tempText.node() as SVGTextElement).getBBox();
      return bbox.width;
    };

    // Split text into words and break into lines
    const words = label.split(/\s+/);
    const lines: string[] = [];
    let currentLine = '';

    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = measureText(testLine);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        // Current line is full, start a new one
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // Single word is too long, truncate it
          let truncated = word;
          while (truncated.length > 0 && measureText(truncated + '...') > maxWidth) {
            truncated = truncated.slice(0, -1);
          }
          lines.push(truncated + '...');
          currentLine = '';
        }
      }
    }

    // Add the last line if it exists
    if (currentLine) {
      lines.push(currentLine);
    }

    // Limit maximum number of lines (e.g., 3 lines max)
    const maxLines = 3;
    let finalLines: string[] = lines;
    
    if (lines.length > maxLines) {
      // Truncate to maxLines and add ellipsis to last line
      finalLines = lines.slice(0, maxLines);
      let lastLine = finalLines[maxLines - 1];
      
      // Try to add ellipsis to last line if there's space
      while (lastLine.length > 0 && measureText(lastLine + '...') > maxWidth) {
        lastLine = lastLine.slice(0, -1);
      }
      finalLines[maxLines - 1] = lastLine + '...';
    }
    
    // Remove the temporary measuring element
    tempText.remove();
    
    // Render the lines
    textSelection.selectAll('tspan').remove();
    const totalHeight = lineHeight * (finalLines.length - 1);

    finalLines.forEach((line, index) => {
      textSelection
        .append('tspan')
        .attr('x', 0)
        .attr('dy', index === 0 ? -(totalHeight / 2) : lineHeight)
        .text(line);
    });
  }

  /**
   * Update node and link opacity based on hovered document ID
   * Lightweight update that doesn't rebuild the entire graph
   */
  updateHoverOpacity(hoveredDocumentId: string | null, d3Nodes: D3GraphNode[], d3Links: D3GraphLink[]): void {
    if (!this.nodeSelection || !this.linkSelection || !this.d3RootGroup) {
      return;
    }

    // Create a map for quick lookup of node opacity
    const nodeOpacityMap = new Map<string, number>();
    d3Nodes.forEach(node => {
      nodeOpacityMap.set(node.id, node.opacity ?? 1);
    });

    // Update node opacity
    this.nodeSelection.select('circle.outline')
      .attr('fill-opacity', (d) => {
        if (hoveredDocumentId) {
          return d.id === hoveredDocumentId ? 1 : 0.1;
        }
        return nodeOpacityMap.get(d.id) ?? 1;
      })
      .classed('graph-node--blurred', (d) => {
        if (hoveredDocumentId) {
          return d.id !== hoveredDocumentId;
        }
        return !!d.isBlurred;
      });

    // Update link opacity
    const linkOpacityMap = new Map<string, number>();
    d3Links.forEach(link => {
      linkOpacityMap.set(link.id, link.opacity ?? 0.05);
    });

    this.linkSelection.select('path.outline')
      .attr('stroke-opacity', (d) => {
        if (hoveredDocumentId) {
          return 0.05; // Blur all links when hovering
        }
        return linkOpacityMap.get(d.id) ?? 0.05;
      })
      .attr('opacity', (d) => {
        if (hoveredDocumentId) {
          return 0.05;
        }
        return linkOpacityMap.get(d.id) ?? 0.05;
      });

    // Hide/show link labels based on hover state
    const linkLabelGroup = this.d3RootGroup.select<SVGGElement>('g.graph-link-labels');
    if (!linkLabelGroup.empty()) {
      const linkLabels = linkLabelGroup.selectAll<SVGGElement, D3GraphLink>('g.graph-link-label');
      if (hoveredDocumentId) {
        // Hide all link labels when hovering
        linkLabels.style('display', 'none');
      } else {
        // Restore link labels visibility (remove inline style to let zoom-based display attribute take over)
        linkLabels.style('display', null);
      }
    }
  }

  /**
   * Destroy the graph and clean up resources
   */
  destroy(): void {
    // Clear simulation stop timeout if it exists
    if (this.simulationStopTimeout) {
      clearTimeout(this.simulationStopTimeout);
      this.simulationStopTimeout = undefined;
    }
    
    // Clear convergence check interval if it exists
    if (this.convergenceCheckInterval) {
      clearInterval(this.convergenceCheckInterval);
      this.convergenceCheckInterval = undefined;
    }
    
    this.d3Simulation?.stop();
    this.d3Simulation = undefined;
    this.d3Svg?.remove();
    this.d3Svg = undefined;
    this.d3RootGroup = undefined;
    this.nodeSelection = undefined;
    this.linkSelection = undefined;
    this.tickCounter = 0;
  }

  /**
   * Build SVG path for a link
   */
  private buildLinkPath(link: D3GraphLink): string {
    const source = link.source as D3GraphNode;
    const target = link.target as D3GraphNode;
    if (!source || !target || source.x === undefined || target.x === undefined) {
      return '';
    }

    const x1 = source.x;
    const y1 = source.y ?? 0;
    const trimmed = this.getTrimmedTargetPoint(link);
    const x2 = trimmed.x;
    const y2 = trimmed.y;
    const curveness = link.__curveness ?? 0;

    if (!curveness) {
      return `M${x1},${y1} L${x2},${y2}`;
    }

    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const distance = Math.sqrt(dx * dx + dy * dy) || 1;
    const maxOffset = Math.min(120, distance * 0.5);
    const offset = curveness * maxOffset;
    const perpX = (-dy / distance) * offset;
    const perpY = (dx / distance) * offset;
    const controlX = midX + perpX;
    const controlY = midY + perpY;
    return `M${x1},${y1} Q${controlX},${controlY} ${x2},${y2}`;
  }

  /**
   * Get transform string for link label
   */
  private getLinkLabelTransform(link: D3GraphLink): string {
    const source = link.source as D3GraphNode;
    const target = link.target as D3GraphNode;
    if (!source || !target || source.x === undefined || target.x === undefined) {
      return 'translate(0,0)';
    }

    const startX = source.x ?? 0;
    const startY = source.y ?? 0;
    const trimmed = this.getTrimmedTargetPoint(link);
    const curveness = link.__curveness ?? 0;

    let midX: number;
    let midY: number;
    let angle: number;
    let normalX: number;
    let normalY: number;

    if (curveness !== 0) {
      const x1 = startX;
      const y1 = startY;
      const x2 = trimmed.x;
      const y2 = trimmed.y;
      const dx = x2 - x1;
      const dy = y2 - y1;
      const distance = Math.sqrt(dx * dx + dy * dy) || 1;
      const maxOffset = Math.min(120, distance * 0.5);
      const offset = curveness * maxOffset;
      const perpX = (-dy / distance) * offset;
      const perpY = (dx / distance) * offset;
      const controlX = (x1 + x2) / 2 + perpX;
      const controlY = (y1 + y2) / 2 + perpY;

      const t = 0.5;
      const mt = 1 - t;
      midX = mt * mt * x1 + 2 * mt * t * controlX + t * t * x2;
      midY = mt * mt * y1 + 2 * mt * t * controlY + t * t * y2;

      const tangentX = 2 * mt * (controlX - x1) + 2 * t * (x2 - controlX);
      const tangentY = 2 * mt * (controlY - y1) + 2 * t * (y2 - controlY);
      const tangentLength = Math.sqrt(tangentX * tangentX + tangentY * tangentY) || 1;
      angle = (Math.atan2(tangentY, tangentX) * 180) / Math.PI;

      normalX = -tangentY / tangentLength;
      normalY = tangentX / tangentLength;
    } else {
      midX = (startX + trimmed.x) / 2;
      midY = (startY + trimmed.y) / 2;
      angle = (Math.atan2(trimmed.y - startY, trimmed.x - startX) * 180) / Math.PI;

      const dx = trimmed.x - startX;
      const dy = trimmed.y - startY;
      const distance = Math.sqrt(dx * dx + dy * dy) || 1;
      normalX = -dy / distance;
      normalY = dx / distance;
    }

    // Normalize angle to 0-360 range
    const normalizedAngle = ((angle % 360) + 360) % 360;
    
    // If angle is between 90 and 270 degrees, the label would be upside-down
    // So we flip it by rotating 180 degrees
    const mirror = normalizedAngle > 90 && normalizedAngle < 270;
    const offsetAmount = mirror ? 2 : -3;
    const adjustedOffset = curveness !== 0 ? offsetAmount * 0.3 : offsetAmount;

    const offsetX = normalX * adjustedOffset;
    const offsetY = normalY * adjustedOffset;
    // Rotate 180 degrees if label would be upside-down
    const rotation = mirror ? normalizedAngle + 180 : normalizedAngle;

    return `translate(${midX + offsetX}, ${midY + offsetY}) rotate(${rotation})`;
  }

  /**
   * Get trimmed target point for link (accounting for node radius)
   */
  private getTrimmedTargetPoint(
    link: D3GraphLink,
    extraOffset: number = 0
  ): { x: number; y: number; dirX: number; dirY: number; angle: number } {
    const source = link.source as D3GraphNode;
    const target = link.target as D3GraphNode;
    const x1 = source?.x ?? 0;
    const y1 = source?.y ?? 0;
    const x2 = target?.x ?? 0;
    const y2 = target?.y ?? 0;
    const dx = x2 - x1;
    const dy = y2 - y1;
    const distance = Math.sqrt(dx * dx + dy * dy) || 1;
    const dirX = dx / distance;
    const dirY = dy / distance;
    const angle = Math.atan2(dy, dx);

    const nodeRadius = (target?.symbolSize || NODE_SIZES.REGULAR) / 2;
    const trim = nodeRadius + this.LINK_ARROW_PADDING + extraOffset;
    const effectiveTrim = Math.min(trim, distance * 0.9);
    const trimmedX = x2 - dirX * effectiveTrim;
    const trimmedY = y2 - dirY * effectiveTrim;

    return {
      x: trimmedX,
      y: trimmedY,
      dirX,
      dirY,
      angle,
    };
  }

  /**
   * Get container size
   */
  private getContainerSize(container: HTMLElement): { width: number; height: number } {
    const rect = container.getBoundingClientRect();
    return {
      width: rect.width || 800,
      height: rect.height || 600,
    };
  }
}

