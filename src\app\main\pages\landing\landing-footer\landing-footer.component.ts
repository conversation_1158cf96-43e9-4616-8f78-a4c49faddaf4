import { Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-landing-footer',
  templateUrl: './landing-footer.component.html',
  styleUrls: ['./landing-footer.component.scss'],
  encapsulation: ViewEncapsulation.Emulated 
})
export class LandingFooterComponent {
  @Input() url1: string | undefined;
  @Output() subscribeEmail = new EventEmitter<string>();

  onSubscribeSubmit(event: Event): void {
    event.preventDefault();
    const form = event.target as HTMLFormElement;
    const data = new FormData(form);
    const email = String(data.get('email') || '').trim();
    if (!email) return;
    this.subscribeEmail.emit(email);
    form.reset();
  }
}
